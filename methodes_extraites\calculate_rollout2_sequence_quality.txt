MÉTHODE : calculate_rollout2_sequence_quality
LIGNE DÉBUT : 4247
SIGNATURE : def calculate_rollout2_sequence_quality(self, sequences: List[Dict]) -> float:
================================================================================

    def calculate_rollout2_sequence_quality(self, sequences: List[Dict]) -> float:
        """
        Calcule la qualité moyenne des séquences générées par le Rollout 2

        Args:
            sequences: Liste des séquences générées

        Returns:
            float: Score de qualité moyen (0-1)
        """
        if not sequences:
            return self.config.zero_value

        total_quality = 0.0
        valid_sequences = 0

        for sequence in sequences:
            # Vérifier si la séquence a des données de qualité
            if isinstance(sequence, dict):
                # Essayer différentes clés de qualité
                quality_score = None

                # Chercher dans les métadonnées d'enrichissement
                enrichment_summary = sequence.get('enrichment_summary', {})
                if 'avg_global_confidence' in enrichment_summary:
                    quality_score = enrichment_summary['avg_global_confidence']

                # Fallback : calculer basé sur la cohérence
                if quality_score is None:
                    sequence_data = sequence.get('sequence_data', [])
                    if sequence_data:
                        # Qualité basée sur la longueur et la cohérence
                        length_score = min(self.config.one_value, len(sequence_data) / self.config.rollout2_sequences_count)  # Optimal selon config
                        quality_score = length_score * self.config.rollout2_confidence_value_high  # Score conservateur

                if quality_score is not None:
                    total_quality += quality_score
                    valid_sequences += self.config.rollout_reward_valid_sequence_increment

        return total_quality / max(self.config.rollout_reward_valid_sequence_increment, valid_sequences)

