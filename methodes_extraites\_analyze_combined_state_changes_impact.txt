MÉTHODE : _analyze_combined_state_changes_impact
LIGNE DÉBUT : 6940
SIGNATURE : def _analyze_combined_state_changes_impact(self, combined_seq: List[str], pbt_seq: List[str], so_seq: List[str]) -> Dict:
================================================================================

    def _analyze_combined_state_changes_impact(self, combined_seq: List[str], pbt_seq: List[str], so_seq: List[str]) -> Dict:
        """
        Analyse l'impact des CHANGEMENTS/TRANSITIONS dans les états combinés (Index 3)
        sur P/B (sans TIE) et S/O

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Analyse comment les transitions d'états combinés affectent les prédictions

        Args:
            combined_seq: Séquence des états combinés (Index 3)
                         Format: ['IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC', ...]
            pbt_seq: Séquence index 4 (P/B/T)
            so_seq: Séquence index 5 (S/O)

        Returns:
            Dictionnaire avec analyse des impacts des changements d'états combinés
        """

        state_changes_impact = {
            'transition_types_analysis': {},      # Analyse par type de transition
            'transition_patterns_impact': {},     # Impact des patterns de transition
            'state_stability_analysis': {},       # Analyse stabilité des états
            'change_strength_metrics': {}         # Métriques de force des changements
        }

        if not combined_seq or len(combined_seq) < 2:
            return {
                'transition_types_analysis': {},
                'transition_patterns_impact': {},
                'state_stability_analysis': {
                    'insufficient_data': True,
                    'sequence_length': len(combined_seq) if combined_seq else 0
                },
                'change_strength_metrics': {
                    'total_transitions': 0,
                    'transition_frequency': 0.0
                }
            }

        # ================================================================
        # 1. DÉTECTION DES TRANSITIONS D'ÉTATS COMBINÉS
        # ================================================================

        transitions = []
        for i in range(1, len(combined_seq)):
            if combined_seq[i-1] != combined_seq[i]:
                from_state = combined_seq[i-1]
                to_state = combined_seq[i]

                # Classifier le type de transition
                transition_type = self._classify_combined_transition_type(from_state, to_state)

                transitions.append({
                    'position': i,
                    'from_state': from_state,
                    'to_state': to_state,
                    'transition_type': transition_type,
                    'transition_name': f"{from_state}_TO_{to_state}"
                })

        # ================================================================
        # 2. ANALYSE PAR TYPE DE TRANSITION
        # ================================================================

        # Grouper transitions par type
        transitions_by_type = {}
        for transition in transitions:
            t_type = transition['transition_type']
            if t_type not in transitions_by_type:
                transitions_by_type[t_type] = []
            transitions_by_type[t_type].append(transition)

        # Analyser impact pour chaque type de transition
        for transition_type, transitions_of_type in transitions_by_type.items():
            if len(transitions_of_type) >= 1:  # Au moins 1 transition de ce type

                # Impact sur P/B après transitions de ce type (sans TIE)
                pb_after_transitions = []
                so_after_transitions = []

                for transition in transitions_of_type:
                    pos = transition['position']

                    # P/B après transition
                    if pos < len(pbt_seq):
                        result = pbt_seq[pos]
                        if result in ['P', 'B']:  # Exclure TIE
                            pb_after_transitions.append(result)

                    # S/O après transition
                    if pos > 0 and pos - 1 < len(so_seq):
                        so_after_transitions.append(so_seq[pos - 1])

                # Analyse P/B pour ce type de transition
                type_pb_analysis = {}
                if pb_after_transitions:
                    p_count = pb_after_transitions.count('P')
                    b_count = pb_after_transitions.count('B')
                    total_pb = p_count + b_count

                    type_pb_analysis = {
                        'player_ratio': p_count / total_pb,
                        'banker_ratio': b_count / total_pb,
                        'dominant_pb': 'P' if p_count > b_count else 'B',
                        'pb_sample_size': total_pb,
                        'pb_bias_strength': abs((p_count / total_pb) - 0.5)
                    }

                # Analyse S/O pour ce type de transition
                type_so_analysis = {}
                if so_after_transitions:
                    s_count = so_after_transitions.count('S')
                    o_count = so_after_transitions.count('O')
                    total_so = s_count + o_count

                    type_so_analysis = {
                        'same_ratio': s_count / total_so,
                        'opposite_ratio': o_count / total_so,
                        'dominant_so': 'S' if s_count > o_count else 'O',
                        'so_sample_size': total_so,
                        'so_bias_strength': abs((s_count / total_so) - 0.5)
                    }

                # Force globale de ce type de transition
                type_strength = 0.0
                if type_pb_analysis and type_so_analysis:
                    pb_strength = type_pb_analysis['pb_bias_strength']
                    so_strength = type_so_analysis['so_bias_strength']
                    # Pondération : P/B poids 2, S/O poids 3 (plus exploitable)
                    type_strength = (pb_strength * 2 + so_strength * 3) / 5

                state_changes_impact['transition_types_analysis'][transition_type] = {
                    'pb_impact': type_pb_analysis,
                    'so_impact': type_so_analysis,
                    'transition_count': len(transitions_of_type),
                    'type_strength': type_strength,
                    'transitions_list': [t['transition_name'] for t in transitions_of_type]
                }

        # ================================================================
        # 3. ANALYSE DES PATTERNS DE TRANSITION SPÉCIFIQUES
        # ================================================================

        # Grouper par transition spécifique (ex: IMPAIR_SYNC_TO_PAIR_DESYNC)
        transitions_by_pattern = {}
        for transition in transitions:
            pattern = transition['transition_name']
            if pattern not in transitions_by_pattern:
                transitions_by_pattern[pattern] = []
            transitions_by_pattern[pattern].append(transition)

        # Analyser les patterns les plus fréquents (minimum 2 occurrences)
        for pattern, pattern_transitions in transitions_by_pattern.items():
            if len(pattern_transitions) >= 2:

                # Impact P/B pour ce pattern spécifique (sans TIE)
                pattern_pb = []
                pattern_so = []

                for transition in pattern_transitions:
                    pos = transition['position']

                    if pos < len(pbt_seq):
                        result = pbt_seq[pos]
                        if result in ['P', 'B']:  # Exclure TIE
                            pattern_pb.append(result)

                    if pos > 0 and pos - 1 < len(so_seq):
                        pattern_so.append(so_seq[pos - 1])

                # Analyse P/B pour ce pattern
                pattern_pb_analysis = {}
                if pattern_pb:
                    p_count = pattern_pb.count('P')
                    b_count = pattern_pb.count('B')
                    total_pb = p_count + b_count

                    pattern_pb_analysis = {
                        'player_ratio': p_count / total_pb,
                        'banker_ratio': b_count / total_pb,
                        'dominant_pb': 'P' if p_count > b_count else 'B',
                        'sample_size': total_pb
                    }

                # Analyse S/O pour ce pattern
                pattern_so_analysis = {}
                if pattern_so:
                    s_count = pattern_so.count('S')
                    o_count = pattern_so.count('O')
                    total_so = s_count + o_count

                    pattern_so_analysis = {
                        'same_ratio': s_count / total_so,
                        'opposite_ratio': o_count / total_so,
                        'dominant_so': 'S' if s_count > o_count else 'O',
                        'sample_size': total_so
                    }

                state_changes_impact['transition_patterns_impact'][pattern] = {
                    'pb_impact': pattern_pb_analysis,
                    'so_impact': pattern_so_analysis,
                    'pattern_frequency': len(pattern_transitions),
                    'from_state': pattern_transitions[0]['from_state'],
                    'to_state': pattern_transitions[0]['to_state']
                }

        # ================================================================
        # 4. ANALYSE DE STABILITÉ DES ÉTATS
        # ================================================================

        # Calculer durée moyenne de chaque état avant changement
        state_durations = {}
        current_state = combined_seq[0] if combined_seq else None
        current_duration = 1

        for i in range(1, len(combined_seq)):
            if combined_seq[i] == current_state:
                current_duration += 1
            else:
                # Fin d'une période d'état stable
                if current_state not in state_durations:
                    state_durations[current_state] = []
                state_durations[current_state].append(current_duration)

                # Nouveau état
                current_state = combined_seq[i]
                current_duration = 1

        # Ajouter la dernière période
        if current_state and current_state not in state_durations:
            state_durations[current_state] = []
        if current_state:
            state_durations[current_state].append(current_duration)

        # Analyser stabilité de chaque état
        stability_analysis = {}
        for state, durations in state_durations.items():
            if durations:
                avg_duration = sum(durations) / len(durations)
                max_duration = max(durations)
                min_duration = min(durations)

                stability_analysis[state] = {
                    'average_duration': avg_duration,
                    'max_duration': max_duration,
                    'min_duration': min_duration,
                    'occurrences': len(durations),
                    'total_time': sum(durations),
                    'stability_score': avg_duration / max(1, len(combined_seq))  # Ratio temps/total
                }

        state_changes_impact['state_stability_analysis'] = stability_analysis

        # ================================================================
        # 5. MÉTRIQUES GLOBALES DE FORCE DES CHANGEMENTS
        # ================================================================

        total_transitions = len(transitions)
        total_sequence_length = len(combined_seq)
        transition_frequency = total_transitions / max(1, total_sequence_length - 1)

        # Force moyenne des transitions
        avg_transition_strength = 0.0
        if transitions_by_type:
            strengths = [data['type_strength'] for data in state_changes_impact['transition_types_analysis'].values() if 'type_strength' in data]
            avg_transition_strength = sum(strengths) / len(strengths) if strengths else 0.0

        # Diversité des transitions
        unique_transition_types = len(transitions_by_type)
        transition_diversity = min(unique_transition_types / 4.0, 1.0)  # Max diversité avec 4 types

        state_changes_impact['change_strength_metrics'] = {
            'total_transitions': total_transitions,
            'transition_frequency': transition_frequency,
            'average_transition_strength': avg_transition_strength,
            'transition_diversity': transition_diversity,
            'unique_transition_types': unique_transition_types,
            'most_frequent_transition_type': max(transitions_by_type.keys(), key=lambda x: len(transitions_by_type[x])) if transitions_by_type else None,
            'has_significant_changes': total_transitions >= self.config.pattern_min_occurrences and avg_transition_strength > self.config.correlation_strength_threshold
        }

        return state_changes_impact

