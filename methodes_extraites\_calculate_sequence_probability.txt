MÉTHODE : _calculate_sequence_probability
LIGNE DÉBUT : 4861
SIGNATURE : def _calculate_sequence_probability(self, sequence: List[str], generation_space: Dict) -> float:
================================================================================

    def _calculate_sequence_probability(self, sequence: List[str], generation_space: Dict) -> float:
        """
        Calcule la probabilité d'une séquence P/B basée sur l'analyse du Rollout 1

        Args:
            sequence: Séquence P/B ['P', 'B', 'P', 'B']
            generation_space: Contexte d'analyse

        Returns:
            float: Probabilité estimée (0-1)
        """
        # Facteurs de probabilité
        probability_factors = []

        # 1. Cohérence avec les prédictions immédiates
        next_prediction_pb = generation_space.get('next_prediction_pb')
        if next_prediction_pb and sequence:
            if sequence[0] == next_prediction_pb:
                probability_factors.append(0.8)  # Forte cohérence
            else:
                probability_factors.append(0.4)  # Faible cohérence
        else:
            probability_factors.append(0.6)  # Neutre

        # 2. Cohérence avec les prédictions S/O
        next_prediction_so = generation_space.get('next_prediction_so')
        if next_prediction_so:
            # Simuler la conversion S/O pour cette séquence
            analyzer_report = {
                'indices_analysis': generation_space.get('indices_analysis', {}),
                'quick_access': generation_space
            }

            # Obtenir le dernier résultat historique
            last_pb = self._get_last_historical_pb_result(analyzer_report)
            if last_pb and sequence:
                predicted_so = 'S' if sequence[0] == last_pb else 'O'
                if predicted_so == next_prediction_so:
                    probability_factors.append(0.85)  # Très forte cohérence S/O
                else:
                    probability_factors.append(0.3)   # Incohérence S/O
            else:
                probability_factors.append(0.5)  # Neutre
        else:
            probability_factors.append(0.5)  # Neutre

        # 3. Analyse des patterns récents
        indices_analysis = generation_space.get('indices_analysis', {})
        pbt_sequence = indices_analysis.get('pbt', {}).get('pbt_sequence', [])

        if len(pbt_sequence) >= 3:
            recent_pb = [r for r in pbt_sequence[-3:] if r in ['P', 'B']]
            if len(recent_pb) >= 2:
                # Analyser la tendance récente
                if recent_pb[-1] == recent_pb[-2]:  # Série récente
                    if sequence[0] == recent_pb[-1]:
                        probability_factors.append(0.7)  # Continue la série
                    else:
                        probability_factors.append(0.6)  # Rupture de série
                else:  # Alternance récente
                    expected_next = 'B' if recent_pb[-1] == 'P' else 'P'
                    if sequence[0] == expected_next:
                        probability_factors.append(0.75)  # Continue l'alternance
                    else:
                        probability_factors.append(0.5)   # Rupture d'alternance
            else:
                probability_factors.append(0.5)  # Pas assez de données
        else:
            probability_factors.append(0.5)  # Pas d'historique

        # 4. Diversité interne de la séquence
        unique_elements = len(set(sequence))
        if unique_elements == 1:  # Toutes identiques (PPPP ou BBBB)
            probability_factors.append(0.4)  # Moins probable
        elif unique_elements == 2:  # Mix P/B
            p_count = sequence.count('P')
            b_count = sequence.count('B')
            if abs(p_count - b_count) <= 1:  # Équilibré
                probability_factors.append(0.8)  # Plus probable
            else:  # Déséquilibré
                probability_factors.append(0.6)  # Moyennement probable

        # 5. Confiance globale du système
        prediction_confidence = generation_space.get('prediction_confidence', 0.5)
        probability_factors.append(prediction_confidence)

        # Calcul de la probabilité finale (moyenne pondérée)
        weights = [0.25, 0.25, 0.2, 0.15, 0.15]  # Pondération par importance
        final_probability = sum(factor * weight for factor, weight in zip(probability_factors, weights))

        return min(1.0, max(0.0, final_probability))

