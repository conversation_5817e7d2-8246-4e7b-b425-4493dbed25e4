MÉTHODE : _extract_desync_periods_strength
LIGNE DÉBUT : 8042
SIGNATURE : def _extract_desync_periods_strength(self, desync_impacts: Dict) -> float:
================================================================================

    def _extract_desync_periods_strength(self, desync_impacts: Dict) -> float:
        """Extrait la force des impacts de périodes de désynchronisation (focus P/B et S/O)"""

        if not desync_impacts:
            return 0.0

        strength_metrics = []

        # Analyser impacts des périodes de désynchronisation
        for period_key, period_data in desync_impacts.items():
            if isinstance(period_data, dict):

                # Force P/B (sans TIE)
                if 'pb_impact' in period_data:
                    pb_impact = period_data['pb_impact']
                    if 'pb_bias_strength' in pb_impact:
                        strength_metrics.append(pb_impact['pb_bias_strength'])

                # Force S/O
                if 'so_impact' in period_data:
                    so_impact = period_data['so_impact']
                    if 'so_bias_strength' in so_impact:
                        strength_metrics.append(so_impact['so_bias_strength'])

                # Force globale de la période
                if 'period_strength' in period_data:
                    strength_metrics.append(period_data['period_strength'])

        return sum(strength_metrics) / len(strength_metrics) if strength_metrics else 0.0

