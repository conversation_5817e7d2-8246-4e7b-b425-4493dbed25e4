MÉTHODE : _analyze_tri_dimensional_impacts
LIGNE DÉBUT : 5786
SIGNATURE : def _analyze_tri_dimensional_impacts(self, impair_pair_seq: List[str], desync_sync_seq: List[str],
================================================================================

    def _analyze_tri_dimensional_impacts(self, impair_pair_seq: List[str], desync_sync_seq: List[str],
                                       combined_seq: List[str], pbt_seq: List[str], so_seq: List[str]) -> Dict:
        """Analyse impacts tri-dimensionnels (NOUVEAU)"""
        tri_impacts = {
            'impair_sync_impacts': {},
            'pair_desync_impacts': {},
            'cross_dimensional_patterns': {},
            'strongest_tri_pattern': None
        }

        # Analyse IMPAIR + SYNC → P/B/T et S/O
        impair_sync_indices = [i for i, (ip, ds) in enumerate(zip(impair_pair_seq, desync_sync_seq))
                              if ip == 'IMPAIR' and ds == 'SYNC']

        if impair_sync_indices:
            # Impact sur P/B/T
            impair_sync_pbt = [pbt_seq[i] for i in impair_sync_indices if i < len(pbt_seq)]
            # Impact sur S/O (ajustement index car S/O commence à manche 2)
            impair_sync_so = [so_seq[i-1] for i in impair_sync_indices if i > 0 and i-1 < len(so_seq)]

            tri_impacts['impair_sync_impacts'] = {
                'pbt_distribution': self._calculate_distribution(impair_sync_pbt, ['P', 'B', 'T']),
                'so_distribution': self._calculate_distribution(impair_sync_so, ['S', 'O']),
                'sample_size': len(impair_sync_indices)
            }

        # Analyse PAIR + DESYNC → P/B/T et S/O
        pair_desync_indices = [i for i, (ip, ds) in enumerate(zip(impair_pair_seq, desync_sync_seq))
                              if ip == 'PAIR' and ds == 'DESYNC']

        if pair_desync_indices:
            pair_desync_pbt = [pbt_seq[i] for i in pair_desync_indices if i < len(pbt_seq)]
            pair_desync_so = [so_seq[i-1] for i in pair_desync_indices if i > 0 and i-1 < len(so_seq)]

            tri_impacts['pair_desync_impacts'] = {
                'pbt_distribution': self._calculate_distribution(pair_desync_pbt, ['P', 'B', 'T']),
                'so_distribution': self._calculate_distribution(pair_desync_so, ['S', 'O']),
                'sample_size': len(pair_desync_indices)
            }

        return tri_impacts

