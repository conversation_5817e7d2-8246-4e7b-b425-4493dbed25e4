MÉTHODE : _calculate_epistemic_uncertainty
LIGNE DÉBUT : 3975
SIGNATURE : def _calculate_epistemic_uncertainty(self, analyzer_report: Dict) -> float:
================================================================================

    def _calculate_epistemic_uncertainty(self, analyzer_report: Dict) -> float:
        """
        Calcule l'incertitude épistémique (incertitude due au manque de connaissance)

        Returns:
            float: Niveau d'incertitude épistémique (0-1)
        """
        uncertainty_factors = []

        # 1. Incertitude basée sur la taille de l'échantillon
        sequence_metadata = analyzer_report.get('sequence_metadata', {})
        total_hands = sequence_metadata.get('total_hands_analyzed', self.config.zero_value)

        # Plus d'échantillons = moins d'incertitude
        sample_uncertainty = max(self.config.zero_value, self.config.one_value - (total_hands / self.config.confidence_calibration['optimal_sample_size']))  # Optimal selon config
        uncertainty_factors.append(sample_uncertainty)

        # 2. Incertitude basée sur la diversité des patterns
        synthesis = analyzer_report.get('synthesis', {})
        dominant_correlations = synthesis.get('dominant_correlations', [])

        # Peu de corrélations = plus d'incertitude
        correlation_uncertainty = max(self.config.zero_value, self.config.one_value - (len(dominant_correlations) / self.config.confidence_calibration['optimal_correlations_count']))  # Optimal selon config
        uncertainty_factors.append(correlation_uncertainty)

        # 3. Incertitude basée sur la cohérence des indices
        indices_analysis = analyzer_report.get('indices_analysis', {})
        indices_count = len([k for k in indices_analysis.keys() if k != 'metadata'])

        # Moins d'indices analysés = plus d'incertitude
        indices_uncertainty = max(self.config.zero_value, self.config.one_value - (indices_count / self.config.confidence_calibration['optimal_indices_count']))  # Optimal selon config
        uncertainty_factors.append(indices_uncertainty)

        # Incertitude épistémique composite
        epistemic_uncertainty = sum(uncertainty_factors) / len(uncertainty_factors)

        return min(self.config.probability_clamp_max, max(self.config.probability_clamp_min, epistemic_uncertainty))

