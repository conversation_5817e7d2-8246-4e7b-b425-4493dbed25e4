MÉTHODE : _calculate_pair_commonality_score
LIGNE DÉBUT : 6078
SIGNATURE : def _calculate_pair_commonality_score(self, pair_consecutive: int) -> float:
================================================================================

    def _calculate_pair_commonality_score(self, pair_consecutive: int) -> float:
        """
        Calcule score de commonalité pour séquences PAIR

        Basé sur la fréquence : 20,000 parties pures PAIR sur toutes possibilités
        """
        if pair_consecutive <= 3:
            return self.config.rollout1_pair_consecutive_very_common  # Très commun
        elif pair_consecutive <= 6:
            return self.config.rollout1_pair_consecutive_common  # Commun
        elif pair_consecutive <= 9:
            return self.config.rollout1_pair_consecutive_fairly_common  # Assez commun
        elif pair_consecutive <= 12:
            return self.config.rollout1_pair_consecutive_less_common  # Moins commun
        else:  # 13+
            return self.config.rollout1_coherence_threshold_excellent  # Rare même pour PAIR

