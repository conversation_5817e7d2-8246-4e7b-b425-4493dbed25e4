MÉTHODE : _correlate_impair_with_combined
LIGNE DÉBUT : 1828
SIGNATURE : def _correlate_impair_with_combined(self, isolated_impairs: List, consecutive_sequences: List, combined_states: List) -> Dict:
================================================================================

    def _correlate_impair_with_combined(self, isolated_impairs: List, consecutive_sequences: List, combined_states: List) -> Dict:
        """Corrèle les IMPAIRS avec les états combinés"""
        correlation = {
            'correlation_strength': 0.0,
            'impair_sync_count': 0,
            'impair_desync_count': 0,
            'dominant_combined': 'IMPAIR_SYNC'
        }

        total_correlations = 0
        impair_desync_count = 0

        # IMPAIRS isolés
        for pos in isolated_impairs:
            if pos - int(self.config.rollout_analyzer_position_offset) < len(combined_states):
                state = combined_states[pos - int(self.config.rollout_analyzer_position_offset)]
                if 'IMPAIR_DESYNC' in state:
                    impair_desync_count += int(self.config.rollout_reward_valid_sequence_increment)
                total_correlations += int(self.config.rollout_reward_valid_sequence_increment)

        # Séquences d'IMPAIRS
        for seq in consecutive_sequences:
            for pos in seq:
                if pos - int(self.config.rollout_analyzer_position_offset) < len(combined_states):
                    state = combined_states[pos - int(self.config.rollout_analyzer_position_offset)]
                    if 'IMPAIR_DESYNC' in state:
                        impair_desync_count += int(self.config.rollout_reward_valid_sequence_increment)
                    total_correlations += int(self.config.rollout_reward_valid_sequence_increment)

        if total_correlations > 0:
            impair_desync_ratio = impair_desync_count / total_correlations
            correlation['correlation_strength'] = abs(impair_desync_ratio - self.config.rollout_analyzer_normality_threshold)
            correlation['impair_desync_count'] = impair_desync_count
            correlation['impair_sync_count'] = total_correlations - impair_desync_count

            if impair_desync_ratio > self.config.rollout_analyzer_normality_threshold:
                correlation['dominant_combined'] = 'IMPAIR_DESYNC'
            else:
                correlation['dominant_combined'] = 'IMPAIR_SYNC'

        return correlation

