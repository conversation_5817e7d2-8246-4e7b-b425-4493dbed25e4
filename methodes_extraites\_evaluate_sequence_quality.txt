MÉTHODE : _evaluate_sequence_quality
LIGNE DÉBUT : 10849
SIGNATURE : def _evaluate_sequence_quality(self, sequence, analyzer_report: Dict) -> Dict:
================================================================================

    def _evaluate_sequence_quality(self, sequence, analyzer_report: Dict) -> Dict:
        """Évaluation qualité séquence pour rollout prédicteur"""
        evaluation = {
            'so_quality_score': 0.0,
            'analyzer_coherence_score': 0.0,
            'total_score': 0.0
        }

        # Adaptation à la structure des données (liste ou dictionnaire)
        if isinstance(sequence, dict):
            sequence_data = sequence.get('sequence_data', [])
        else:
            sequence_data = sequence  # C'est déjà une liste

        # Évaluation qualité S/O (priorité absolue)
        so_sequence = [hand.get('so_conversion', 'S') for hand in sequence_data if isinstance(hand, dict)]

        # Diversité S/O
        if len(set(so_sequence)) > 1:
            evaluation['so_quality_score'] += self.config.rollout3_quality_bonus_small

        # Cohérence avec corrélations découvertes
        correlations = analyzer_report.get('correlation_analysis', {})
        combined_correlation = correlations.get('combined', {})

        if combined_correlation.get('std_dev', 1.0) < 0.2:  # Corrélation fiable
            evaluation['so_quality_score'] += self.config.rollout3_quality_bonus_medium

        # Cohérence avec analyseur
        consecutive_analysis = analyzer_report.get('consecutive_analysis', {})
        if consecutive_analysis.get('impair_alert_level', 0) >= 1:
            # Vérifier si séquence évite continuation IMPAIR
            if not any(hand.get('parity') == 'IMPAIR' for hand in sequence_data if isinstance(hand, dict)):
                evaluation['analyzer_coherence_score'] += self.config.rollout3_neutral_evaluation_value

        # Score total pondéré
        evaluation['total_score'] = (
            evaluation['so_quality_score'] * 0.7 +  # 70% priorité S/O
            evaluation['analyzer_coherence_score'] * 0.3  # 30% cohérence analyseur
        )

        return evaluation

