MÉTHODE : _analyze_combined_to_pbt_impact
LIGNE DÉBUT : 5706
SIGNATURE : def _analyze_combined_to_pbt_impact(self, combined_seq: List[str], pbt_seq: List[str]) -> Dict:
================================================================================

    def _analyze_combined_to_pbt_impact(self, combined_seq: List[str], pbt_seq: List[str]) -> Dict:
        """Analyse impact COMBINÉ → P/B (FOCUS P/B UNIQUEMENT)"""
        if len(combined_seq) != len(pbt_seq):
            return {'alignment_error': True}

        # États combinés possibles
        combined_states = ['IMPAIR_SYNC', 'IMPAIR_DESYNC', 'PAIR_SYNC', 'PAIR_DESYNC']

        impact_analysis = {}

        for state in combined_states:
            # Compter les occurrences de chaque état → P/B/T
            state_p_count = sum(1 for c, pbt in zip(combined_seq, pbt_seq) if c == state and pbt == 'P')
            state_b_count = sum(1 for c, pbt in zip(combined_seq, pbt_seq) if c == state and pbt == 'B')
            state_t_count = sum(1 for c, pbt in zip(combined_seq, pbt_seq) if c == state and pbt == 'T')

            # Total P/B uniquement (Ties exclus de l'analyse d'impact)
            total_state_pb = state_p_count + state_b_count
            total_state_all = state_p_count + state_b_count + state_t_count

            if total_state_pb > 0:  # Au moins une occurrence P/B
                impact_analysis[state] = {
                    # Impact sur P/B uniquement (Ties exclus de l'analyse d'impact)
                    'to_player': state_p_count / total_state_pb,
                    'to_banker': state_b_count / total_state_pb,
                    'dominant_outcome': 'P' if state_p_count > state_b_count else 'B',
                    # Métadonnées pour traçabilité
                    'pb_occurrences': total_state_pb,
                    'tie_occurrences': state_t_count,
                    'total_occurrences': total_state_all
                }

        # Identification du pattern le plus fort (P/B uniquement)
        strongest_impact = max(impact_analysis.items(),
                             key=lambda x: max(x[1]['to_player'], x[1]['to_banker']),
                             default=('none', {'to_player': 0}))

        return {
            'state_impacts': impact_analysis,
            'strongest_pattern': strongest_impact[0],
            'strongest_impact_value': max(strongest_impact[1]['to_player'], strongest_impact[1]['to_banker']) if strongest_impact[0] != 'none' else 0,
            'overall_impact_strength': self._calculate_combined_pbt_impact_strength(impact_analysis)
        }

