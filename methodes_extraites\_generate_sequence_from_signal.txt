MÉTHODE : _generate_sequence_from_signal
LIGNE DÉBUT : 4620
SIGNATURE : def _generate_sequence_from_signal(self, signal: Dict, generation_space: Dict) -> List[str]:
================================================================================

    def _generate_sequence_from_signal(self, signal: Dict, generation_space: Dict) -> List[str]:
        """
        Génère une séquence spécifique basée sur un signal du Rollout 1
        """
        signal_type = signal.get('signal_type', self.config.rollout2_default_signal_type)
        signal_name = signal.get('signal_name', self.config.rollout2_default_signal_name)
        target_outcome = signal.get('target_outcome')

        # Longueur de séquence FIXE selon spécifications AZR
        sequence_length = self.config.rollout2_fixed_length  # Toujours 4 P/B

        sequence = []

        if signal_type == self.config.rollout2_so_prediction_keyword and target_outcome:
            # Génération basée sur prédiction S/O
            sequence = self._generate_so_based_sequence(target_outcome, sequence_length, generation_space)
        elif signal_type == self.config.rollout2_pb_prediction_keyword:
            # Génération basée sur prédiction P/B
            if self.config.rollout2_player_keyword in signal_name:
                sequence = self._generate_pb_sequence(self.config.rollout2_player_result, sequence_length, generation_space)
            elif self.config.rollout2_banker_keyword in signal_name:
                sequence = self._generate_pb_sequence(self.config.rollout2_banker_result, sequence_length, generation_space)
        elif self.config.rollout2_pair_sync_keyword in signal_name:
            # Exploitation spécifique PAIR_SYNC
            sequence = self._generate_pair_sync_sequence(sequence_length, generation_space)
        elif self.config.rollout2_impair_sync_keyword in signal_name:
            # Exploitation spécifique IMPAIR_SYNC
            sequence = self._generate_impair_sync_sequence(sequence_length, generation_space)
        else:
            # Génération générique basée sur le signal
            sequence = self._generate_generic_signal_sequence(signal, sequence_length, generation_space)

        return sequence

