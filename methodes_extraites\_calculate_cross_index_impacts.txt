MÉTHODE : _calculate_cross_index_impacts
LIGNE DÉBUT : 2967
SIGNATURE : def _calculate_cross_index_impacts(self, all_indices: Dict) -> Dict:
================================================================================

    def _calculate_cross_index_impacts(self, all_indices: Dict) -> Dict:
        """
        Calcule les impacts croisés entre les différents indices

        FOCUS : Corrélations entre indices pour optimiser les prédictions
        """
        cross_impacts = {}

        # Impact IMPAIR/PAIR → P/B/T
        impair_pair = all_indices.get('impair_pair', {})
        correlations = impair_pair.get('correlations', {})

        cross_impacts['impair_pair_to_pbt'] = {
            'impair_to_player': correlations.get('impair_to_player', 0.5),
            'impair_to_banker': correlations.get('impair_to_banker', 0.5),
            'pair_to_player': correlations.get('pair_to_player', 0.5),
            'pair_to_banker': correlations.get('pair_to_banker', 0.5),
            'impair_pb_hands': correlations.get('impair_pb_hands', 0),
            'pair_pb_hands': correlations.get('pair_pb_hands', 0)
        }

        # Impact INDEX COMBINÉ → S/O (LE PLUS IMPORTANT)
        combined = all_indices.get('combined', {})
        combined_sequence = combined.get('combined_sequence', [])

        # Analyser l'impact de chaque état combiné sur S/O
        so = all_indices.get('so', {})
        so_sequence = so.get('so_sequence', [])

        state_impacts = {}
        if len(combined_sequence) == len(so_sequence):
            # Compter les impacts par état
            for i, state in enumerate(combined_sequence):
                if state not in state_impacts:
                    state_impacts[state] = {'to_s': 0, 'to_o': 0, 'total_occurrences': 0}

                if i < len(so_sequence):
                    so_result = so_sequence[i]
                    state_impacts[state]['total_occurrences'] += 1

                    if so_result == 'S':
                        state_impacts[state]['to_s'] += 1
                    elif so_result == 'O':
                        state_impacts[state]['to_o'] += 1

            # Calculer les ratios
            for state, data in state_impacts.items():
                total = data['total_occurrences']
                if total > 0:
                    data['to_s_ratio'] = data['to_s'] / total
                    data['to_o_ratio'] = data['to_o'] / total
                else:
                    data['to_s_ratio'] = 0.5
                    data['to_o_ratio'] = 0.5

        cross_impacts['combined_to_so'] = {
            'state_impacts': state_impacts,
            'total_states_analyzed': len(state_impacts)
        }

        # Impact SYNC/DESYNC → P/B
        desync_sync = all_indices.get('desync_sync', {})
        sync_sequence = desync_sync.get('sync_sequence', [])

        pbt = all_indices.get('pbt', {})
        pbt_sequence = pbt.get('pbt_sequence', [])

        sync_pb_stats = {'sync_p': 0, 'sync_b': 0, 'desync_p': 0, 'desync_b': 0}

        if len(sync_sequence) == len(pbt_sequence):
            for i, sync_state in enumerate(sync_sequence):
                if i < len(pbt_sequence) and pbt_sequence[i] in ['P', 'B']:
                    pb_result = pbt_sequence[i]

                    if sync_state == 'SYNC':
                        if pb_result == 'P':
                            sync_pb_stats['sync_p'] += 1
                        else:
                            sync_pb_stats['sync_b'] += 1
                    elif sync_state == 'DESYNC':
                        if pb_result == 'P':
                            sync_pb_stats['desync_p'] += 1
                        else:
                            sync_pb_stats['desync_b'] += 1

        # Calculer les ratios SYNC/DESYNC → P/B
        sync_total = sync_pb_stats['sync_p'] + sync_pb_stats['sync_b']
        desync_total = sync_pb_stats['desync_p'] + sync_pb_stats['desync_b']

        cross_impacts['desync_sync_to_pbt'] = {
            'sync_to_player': sync_pb_stats['sync_p'] / max(1, sync_total),
            'sync_to_banker': sync_pb_stats['sync_b'] / max(1, sync_total),
            'desync_to_player': sync_pb_stats['desync_p'] / max(1, desync_total),
            'desync_to_banker': sync_pb_stats['desync_b'] / max(1, desync_total),
            'sync_pb_hands': sync_total,
            'desync_pb_hands': desync_total
        }

        return cross_impacts

