LISTE COMPLÈTE DES 140 MÉTHODES DU CLUSTER 0
===========================================

Basée sur l'analyse manuelle méthodique de classification_manuelle_methodes.txt
Ces méthodes constituent l'ensemble complet à universaliser dans AZRCluster.

ROLLOUT 1 - ANALYSEUR (51 méthodes) :
====================================

1. _rollout_analyzer
2. _analyze_combined_state_changes_impact
3. _analyze_combined_structural_bias
4. _analyze_impair_consecutive_bias
5. _analyze_sync_alternation_bias
6. _analyze_pair_priority_2_autonomous
7. _correlate_bias_to_pb_variations
8. _correlate_impair_with_sync
9. _correlate_bias_to_so_variations
10. _correlate_impair_with_combined
11. _correlate_impair_with_pb
12. _correlate_impair_with_so
13. _synthesize_complete_analysis
14. _analyze_combined_to_pbt_impact
15. _analyze_combined_to_so_impact
16. _analyze_complete_combined_index
17. _analyze_complete_cross_impacts
18. _analyze_complete_desync_sync_index
19. _analyze_complete_impair_pair_index
20. _analyze_complete_pbt_index
21. _analyze_complete_so_index
22. _analyze_consecutive_length_impact
23. _analyze_correlation_trend
24. _analyze_correlations_std_dev
25. _analyze_desync_periods_impact
26. _analyze_desync_sync_to_pbt_impact
27. _analyze_desync_sync_to_so_impact
28. _analyze_impair_pair_to_so_impact
29. _analyze_sequence_consistency
30. _analyze_temporal_correlation_evolution
31. _analyze_transition_moments_impact
32. _analyze_tri_dimensional_impacts
33. _analyze_variations_impact_on_outcomes
34. _generate_bias_signals_summary
35. _generate_bias_generation_guidance
36. _generate_bias_quick_access
37. _generate_bias_exploitation_synthesis
38. _generate_complete_synthesis
39. _generate_priority_based_synthesis_autonomous
40. _generate_signals_summary
41. _calculate_cross_index_impacts
42. _calculate_global_strength_metrics
43. _calculate_variations_impact
44. _calculate_combined_pbt_impact_strength
45. _calculate_combined_so_impact_strength
46. _calculate_correlation_stability
47. _calculate_evolution_strength
48. _calculate_overall_impact_strength
49. _calculate_pattern_stability
50. _calculate_phase_correlation_strength
51. _calculate_statistical_significance

ROLLOUT 2 - GÉNÉRATEUR (42 méthodes) :
=====================================

1. _rollout_generator
2. _generate_sequences_from_signals
3. _define_optimized_generation_space
4. _generate_pb_sequence
5. _calculate_sequence_probability
6. _convert_pb_sequence_to_so
7. _generate_fallback_sequences
8. _generate_all_possible_sequences
9. calculate_rollout2_reward
10. _generate_combined_index_sequence
11. _generate_generation_guidance
12. _generate_generic_signal_sequence
13. _generate_impair_pair_optimized_sequence
14. _generate_impair_sync_sequence
15. _generate_pair_sync_sequence
16. _generate_sequence_from_signal
17. _generate_so_based_sequence
18. _generate_so_pattern_sequence
19. _generate_sync_based_sequence
20. _generate_quick_access
21. _convert_pb_sequence_to_so_with_history
22. _classify_confidence_level
23. _enrich_sequences_with_complete_indexes
24. _get_last_historical_pb_result
25. calculate_rollout2_diversity_score
26. calculate_rollout2_sequence_quality
27. _define_complete_generation_space_DEPRECATED
28. _calculate_sequence_quality_metrics
29. _calculate_sequence_score
30. _calculate_asymmetric_impair_alert_level
31. _calculate_asymmetric_pair_alert_level
32. _calculate_asymmetric_significance
33. _calculate_distribution
34. _calculate_impair_rarity_score
35. _calculate_pair_commonality_score
36. _calculate_phase_impair_pair_pb_correlation
37. _calculate_phase_impair_pair_so_correlation
38. _calculate_phase_sync_desync_pb_correlation
39. _calculate_phase_sync_desync_so_correlation
40. _calculate_rupture_probability
41. _calculate_strength_distribution
42. _calculate_variance

ROLLOUT 3 - PRÉDICTEUR (19 méthodes) :
=====================================

1. _rollout_predictor
2. _evaluate_sequence_quality
3. _calculate_cluster_confidence
4. _select_best_sequence
5. _validate_sequence_logic
6. calculate_rollout3_reward
7. _calculate_cluster_confidence_azr_calibrated
8. _assess_overall_quality
9. _assess_risk_reward_ratio
10. _assess_sample_size_adequacy
11. _evaluate_fallback_alignment
12. _evaluate_signal_alignment
13. _extract_next_hand_prediction
14. calculate_rollout3_risk_factor
15. calculate_cluster_total_reward
16. _calculate_epistemic_uncertainty
17. _calculate_confidence_risk_factors
18. _calculate_rollout_consensus
19. _calculate_confidence_level

BASE - UTILITAIRES (28 méthodes) :
=================================

1. _count_consecutive_pattern
2. _find_consecutive_sequences
3. _find_consecutive_sequences_with_positions
4. _extract_combined_state_changes_strength
5. _extract_consecutive_length_strength
6. _extract_desync_periods_strength
7. _extract_temporal_evolution_strength
8. _extract_transition_moments_strength
9. _identify_best_prediction_context
10. _identify_desync_periods
11. _identify_dominant_desync_sync_so_pattern
12. _identify_dominant_impair_pair_so_pattern
13. _identify_enhanced_dominant_correlations
14. _identify_enhanced_high_confidence_zones
15. _identify_improbability_zones
16. _classify_combined_transition_type
17. _update_performance_metrics
18. _generate_exploitation_recommendation
19. _generate_temporal_recommendation
20. get_max_sequence_length
21. get_max_so_conversions
22. is_game_complete
23. _calculate_temporal_consistency
24. _calculate_temporal_predictability
25. _calculate_variation_consistency
26. _calculate_variation_strength_analysis
27. _calculate_strength_distribution
28. _calculate_variance

TOTAL : 140 MÉTHODES DU CLUSTER 0
=================================

RÉPARTITION FINALE :
- Rollout 1 (Analyseur) : 51 méthodes
- Rollout 2 (Générateur) : 42 méthodes
- Rollout 3 (Prédicteur) : 19 méthodes
- BASE (Utilitaires) : 28 méthodes

Ces 140 méthodes constituent l'ensemble complet du cluster 0 et de ses
rollouts 1, 2, 3 à universaliser dans la classe AZRCluster selon la
méthodologie d'universalisation AZR.

PROCHAINE ÉTAPE :
================
Universaliser ces 140 méthodes en appliquant la méthodologie
d'universalisation AZR pour créer des méthodes universelles
avec paramètres centralisés dans AZRConfig.
