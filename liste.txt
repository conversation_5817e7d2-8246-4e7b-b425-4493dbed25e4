🎮 Lancement de l'interface graphique AZR
Interface avec prédictions en temps réel, entraînement intégré
et toutes les fonctionnalités AZR disponibles.
2025-06-07 22:48:57,312 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-07 22:48:57,312 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-07 22:48:57,313 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-07 22:48:57,313 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-07 22:48:57,313 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-07 22:48:57,313 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-07 22:48:57,314 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-07 22:48:57,319 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-07 22:48:57,319 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-07 22:48:57,319 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-07 22:48:57,319 - INFO - 🧠 Modèle AZR Baccarat initialisé avec persistance intelligente
2025-06-07 22:48:57,320 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-07 22:48:58,202 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-07 22:48:58,202 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-07 22:49:00,159 - INFO - 🔥 Brûlage initialisé: PAIR → État initial: SYNC
2025-06-07 22:49:00,160 - INFO - 📊 Séquence complète initialisée: ['PAIR']
2025-06-07 22:49:00,162 - INFO - 🔥 Cartes brûlées initialisées: PAIR → SYNC
2025-06-07 22:49:01,364 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:01,364 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:01,438 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:01,438 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:01,439 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:01,440 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:01,440 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:01,441 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:01,441 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:01,442 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:01,443 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:01,443 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:01,444 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:01,444 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:01,445 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:01,445 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:01,445 - INFO - 📊 Manche #0.0: PLAYER PAIR SYNC PAIR_SYNC → Prédiction: S
2025-06-07 22:49:01,446 - INFO - ✅ Manche traitée: P/B#0.0 PLAYER PAIR SYNC -- → Prédiction: S
2025-06-07 22:49:04,354 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.000
2025-06-07 22:49:04,356 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:04,356 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:04,362 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:04,363 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:04,364 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:04,364 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:04,365 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:04,365 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:04,366 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:04,366 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:04,367 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:04,368 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:04,368 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:04,369 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:04,370 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:04,370 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:04,370 - INFO - 📊 Manche #1.0: BANKER PAIR SYNC PAIR_SYNC → Prédiction: S
2025-06-07 22:49:04,371 - INFO - ✅ Manche traitée: P/B#1.0 BANKER PAIR SYNC O → Prédiction: S
2025-06-07 22:49:05,370 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.500
2025-06-07 22:49:05,371 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:05,372 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:05,377 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:05,378 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:05,379 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:05,379 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:05,380 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:05,381 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:05,382 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:05,382 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:05,383 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:05,383 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:05,384 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:05,384 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:05,385 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:05,385 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:05,386 - INFO - 📊 Manche #2.0: BANKER IMPAIR DESYNC IMPAIR_DESYNC → Prédiction: O
2025-06-07 22:49:05,386 - INFO - ✅ Manche traitée: P/B#2.0 BANKER IMPAIR DESYNC S → Prédiction: O
2025-06-07 22:49:06,346 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:06,347 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:06,353 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:06,353 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:06,354 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:06,354 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:06,355 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:06,355 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:06,356 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:06,356 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:06,357 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:06,357 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:06,358 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:06,358 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:06,359 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:06,359 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:06,360 - INFO - 📊 Manche #3.0: TIE IMPAIR SYNC IMPAIR_SYNC → Prédiction: S
2025-06-07 22:49:06,360 - INFO - ✅ Manche traitée: P/B#3.0 TIE IMPAIR SYNC -- → Prédiction: S
2025-06-07 22:49:07,777 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.667
2025-06-07 22:49:07,779 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:07,779 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:07,785 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:07,786 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:07,787 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:07,787 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:07,788 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:07,789 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:07,790 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:07,790 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:07,791 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:07,791 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:07,792 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:07,792 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:07,793 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:07,794 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:07,794 - INFO - 📊 Manche #3.0: BANKER PAIR SYNC PAIR_SYNC → Prédiction: S
2025-06-07 22:49:07,795 - INFO - ✅ Manche traitée: P/B#3.0 BANKER PAIR SYNC S → Prédiction: S
2025-06-07 22:49:08,549 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:08,550 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:08,556 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:08,556 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:08,558 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:08,558 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:08,559 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:08,560 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:08,561 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:08,561 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:08,562 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:08,562 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:08,563 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:08,563 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:08,564 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:08,565 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:08,565 - INFO - 📊 Manche #4.0: TIE PAIR SYNC PAIR_SYNC → Prédiction: S
2025-06-07 22:49:08,566 - INFO - ✅ Manche traitée: P/B#4.0 TIE PAIR SYNC -- → Prédiction: S
2025-06-07 22:49:10,114 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:10,114 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:10,121 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:10,121 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:10,123 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:10,123 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:10,124 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:10,124 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:10,125 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:10,126 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:10,126 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:10,127 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:10,128 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:10,128 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:10,129 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:10,129 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:10,129 - INFO - 📊 Manche #4.0: TIE IMPAIR DESYNC IMPAIR_DESYNC → Prédiction: O
2025-06-07 22:49:10,130 - INFO - ✅ Manche traitée: P/B#4.0 TIE IMPAIR DESYNC -- → Prédiction: O
2025-06-07 22:49:11,120 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:11,121 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:11,127 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,127 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,128 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,128 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,129 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,129 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,130 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,131 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,131 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,132 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,132 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,133 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,133 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:11,134 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:11,134 - INFO - 📊 Manche #4.0: TIE PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 22:49:11,134 - INFO - ✅ Manche traitée: P/B#4.0 TIE PAIR DESYNC -- → Prédiction: O
2025-06-07 22:49:11,953 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:11,953 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:11,960 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,961 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,963 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,963 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,964 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,964 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,965 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,965 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,967 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,967 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,968 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:11,968 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:11,969 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:11,969 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:11,970 - INFO - 📊 Manche #4.0: TIE PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 22:49:11,971 - INFO - ✅ Manche traitée: P/B#4.0 TIE PAIR DESYNC -- → Prédiction: O
2025-06-07 22:49:12,724 - INFO - ✅ Validation: Prédit=O, Réel=S, Correct=False, Précision=0.500
2025-06-07 22:49:12,726 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:12,726 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:12,731 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:12,732 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:12,732 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:12,733 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:12,733 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:12,734 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:12,734 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:12,735 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:12,735 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:12,736 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:12,737 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:12,737 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:12,738 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:12,740 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:12,741 - ERROR - ❌ Erreur sauvegarde intelligence AZR: 'AZRConfig' object has no attribute 'cluster_count'
2025-06-07 22:49:12,741 - INFO - 📊 Manche #4.0: BANKER IMPAIR SYNC IMPAIR_SYNC → Prédiction: S
2025-06-07 22:49:12,742 - INFO - ✅ Manche traitée: P/B#4.0 BANKER IMPAIR SYNC S → Prédiction: S
2025-06-07 22:49:13,568 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.400
2025-06-07 22:49:13,569 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:13,570 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:13,576 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:13,576 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:13,577 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:13,578 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:13,579 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:13,579 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:13,580 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:13,580 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:13,581 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:13,582 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:13,583 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:13,583 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:13,584 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:13,584 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:13,586 - INFO - 📊 Manche #5.0: PLAYER IMPAIR DESYNC IMPAIR_DESYNC → Prédiction: O
2025-06-07 22:49:13,586 - INFO - ✅ Manche traitée: P/B#5.0 PLAYER IMPAIR DESYNC O → Prédiction: O
2025-06-07 22:49:14,381 - INFO - ✅ Validation: Prédit=O, Réel=S, Correct=False, Précision=0.333
2025-06-07 22:49:14,382 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:14,382 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:14,387 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:14,388 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:14,389 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:14,389 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:14,390 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:14,390 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:14,391 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:14,392 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:14,393 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:14,394 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:14,394 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:14,394 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:14,395 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:14,396 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:14,396 - INFO - 📊 Manche #6.0: PLAYER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 22:49:14,397 - INFO - ✅ Manche traitée: P/B#6.0 PLAYER PAIR DESYNC S → Prédiction: O
2025-06-07 22:49:15,112 - INFO - ✅ Validation: Prédit=O, Réel=O, Correct=True, Précision=0.429
2025-06-07 22:49:15,114 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:15,114 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:15,119 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,120 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,121 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,121 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,122 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,122 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,123 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,123 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,124 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,124 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,125 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,125 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,126 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:15,126 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:15,126 - INFO - 📊 Manche #7.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 22:49:15,127 - INFO - ✅ Manche traitée: P/B#7.0 BANKER PAIR DESYNC O → Prédiction: O
2025-06-07 22:49:15,834 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:15,835 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:15,841 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,841 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,842 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,843 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,843 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,844 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,844 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,845 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,846 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,846 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,847 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:15,847 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:15,848 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:15,850 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:15,850 - INFO - 📊 Manche #8.0: TIE PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 22:49:15,850 - INFO - ✅ Manche traitée: P/B#8.0 TIE PAIR DESYNC -- → Prédiction: O
2025-06-07 22:49:16,768 - INFO - ✅ Validation: Prédit=O, Réel=S, Correct=False, Précision=0.375
2025-06-07 22:49:16,770 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:16,770 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:16,777 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:16,777 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:16,778 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:16,778 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:16,780 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:16,780 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:16,780 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:16,781 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:16,782 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:16,782 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:16,783 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:16,783 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:16,784 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:16,784 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:16,785 - INFO - 📊 Manche #8.0: BANKER IMPAIR SYNC IMPAIR_SYNC → Prédiction: S
2025-06-07 22:49:16,785 - INFO - ✅ Manche traitée: P/B#8.0 BANKER IMPAIR SYNC S → Prédiction: S
2025-06-07 22:49:17,530 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.333
2025-06-07 22:49:17,532 - ERROR - Erreur rollout analyzer cluster 0: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:17,532 - ERROR - Erreur rollout analyzer cluster 1: 'float' object cannot be interpreted as an integer
2025-06-07 22:49:17,538 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:17,538 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:17,539 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:17,539 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:17,540 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:17,540 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:17,541 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:17,542 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:17,543 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:17,543 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:17,544 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'
2025-06-07 22:49:17,544 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5691, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7105, in _generate_fallback_sequences
    'justification': self.config.rollout2_fallback_justification_1
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_fallback_justification_1'

2025-06-07 22:49:17,544 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_boost_factor'
2025-06-07 22:49:17,545 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 22:49:17,545 - INFO - 📊 Manche #9.0: PLAYER PAIR SYNC PAIR_SYNC → Prédiction: S
2025-06-07 22:49:17,545 - INFO - ✅ Manche traitée: P/B#9.0 PLAYER PAIR SYNC O → Prédiction: S
2025-06-07 22:49:20,375 - INFO - 🔄 Session AZR réinitialisée
2025-06-07 22:49:20,378 - INFO - 💾 Partie courante réinitialisée après sauvegarde - Intelligence préservée
2025-06-07 22:49:20,378 - INFO - 🔄 SOFT RESET : Nouvelle partie initialisée - Intelligence préservée