MÉTHODE : _generate_pair_sync_sequence
LIGNE DÉBUT : 5047
SIGNATURE : def _generate_pair_sync_sequence(self, sequence_length: int, generation_space: Dict) -> List[str]:
================================================================================

    def _generate_pair_sync_sequence(self, sequence_length: int, generation_space: Dict) -> List[str]:
        """
        Génère une séquence exploitant le pattern PAIR_SYNC

        LONGUEUR FIXE : Toujours 4 P/B selon spécifications AZR

        Args:
            sequence_length: Ignoré - longueur fixe à 4 P/B
            generation_space: Espace de génération avec contexte
        """
        sequence = []

        # Analyser les corrélations PAIR_SYNC dans les données
        combined_analysis = generation_space.get('indices_analysis', {}).get('combined', {})
        combined_sequence = combined_analysis.get('combined_sequence', [])

        # Trouver les patterns PAIR_SYNC et leurs outcomes
        pair_sync_outcomes = []
        pbt_sequence = generation_space.get('indices_analysis', {}).get('pbt', {}).get('pbt_sequence', [])

        if len(combined_sequence) == len(pbt_sequence):
            for i, state in enumerate(combined_sequence):
                if state == 'PAIR_SYNC' and pbt_sequence[i] in ['P', 'B']:
                    pair_sync_outcomes.append(pbt_sequence[i])

        # Déterminer le résultat le plus fréquent pour PAIR_SYNC
        if pair_sync_outcomes:
            p_count = pair_sync_outcomes.count('P')
            b_count = pair_sync_outcomes.count('B')
            preferred_outcome = 'P' if p_count >= b_count else 'B'
        else:
            preferred_outcome = 'B'  # Valeur par défaut basée sur les découvertes

        # Générer la séquence avec alternance autour du preferred_outcome (longueur fixe 4)
        for i in range(self.config.rollout2_fixed_length):
            if i % 2 == 0:
                sequence.append(preferred_outcome)
            else:
                sequence.append('P' if preferred_outcome == 'B' else 'B')

        return sequence

