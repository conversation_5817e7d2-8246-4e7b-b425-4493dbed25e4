MÉTHODE : _find_consecutive_sequences
LIGNE DÉBUT : 5996
SIGNATURE : def _find_consecutive_sequences(self, sequence: List[str], pattern: str) -> List[int]:
================================================================================

    def _find_consecutive_sequences(self, sequence: List[str], pattern: str) -> List[int]:
        """Trouve toutes les séquences consécutives d'un pattern"""
        consecutive_lengths = []
        current_length = 0

        for item in sequence:
            if item == pattern:
                current_length += 1
            else:
                if current_length > 0:
                    consecutive_lengths.append(current_length)
                    current_length = 0

        # Ajouter la dernière séquence si elle se termine par le pattern
        if current_length > 0:
            consecutive_lengths.append(current_length)

        return consecutive_lengths





    # ========================================================================
    # MÉTHODES D'ANALYSE ASYMÉTRIQUE IMPAIR/PAIR
    # ========================================================================

