MÉTHODE : _apply_c2_short_patterns_specialization
LIGNE DÉBUT : 1287
SIGNATURE : def _apply_c2_short_patterns_specialization(self, base_analysis: Dict) -> Dict:
================================================================================

    def _apply_c2_short_patterns_specialization(self, base_analysis: Dict) -> Dict:
        """
        🎯 C2 SPÉCIALISATION - Application des bonus patterns courts

        Applique la spécialisation C2 EN PLUS de la logique de base.
        """
        c2_specialization = {
            'specialization_type': 'patterns_courts_2_3_manches',
            'cluster_id': self.cluster_id,
            'fenetre_recente_optimisee': self.config.get_cluster_recent_window_size(self.cluster_id)
        }

        # Calculer le bonus total de spécialisation C2
        total_bonus = self.config.zero_value

        # Bonus des analyses IMPAIR spécialisées
        impair_analysis = base_analysis.get('impair_bias', {})
        total_bonus += impair_analysis.get('c2_total_bonus', self.config.zero_value)

        # Bonus des analyses SYNC spécialisées
        sync_analysis = base_analysis.get('sync_bias', {})
        total_bonus += sync_analysis.get('c2_total_bonus', self.config.zero_value)

        # Bonus global spécialisation C2
        c2_specialization['specialization_bonus'] = min(self.config.one_value, total_bonus)
        c2_specialization['impair_bonus'] = impair_analysis.get('c2_total_bonus', self.config.zero_value)
        c2_specialization['sync_bonus'] = sync_analysis.get('c2_total_bonus', self.config.zero_value)

        # Métriques spécialisées C2
        c2_specialization['short_patterns_detected'] = (
            impair_analysis.get('c2_recent_sequences_count', 0) +
            sync_analysis.get('c2_recent_breaks_count', 0)
        )

        c2_specialization['reactivity_score'] = (
            impair_analysis.get('c2_reactivity_bonus', self.config.zero_value) +
            sync_analysis.get('c2_sync_variability_recent', self.config.zero_value)
        ) / 2

        return c2_specialization

