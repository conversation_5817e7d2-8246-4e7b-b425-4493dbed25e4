MÉTHODE : _calculate_overall_impact_strength
LIGNE DÉBUT : 6370
SIGNATURE : def _calculate_overall_impact_strength(self, cross_impacts: Dict) -> float:
================================================================================

    def _calculate_overall_impact_strength(self, cross_impacts: Dict) -> float:
        """
        Calcule la force globale de TOUS les impacts croisés analysés

        IMPORTANT: Synthèse finale de tous les impacts indices 1-3 → indices 4-5
        Pondération selon l'importance et la fiabilité de chaque type d'impact

        Args:
            cross_impacts: Dictionnaire complet avec tous les impacts croisés analysés

        Returns:
            Score de force globale d'impact (0.0 à 1.0)
        """

        if not cross_impacts or len(cross_impacts) == 0:
            return 0.0

        total_weighted_strength = 0.0
        total_weights = 0.0

        # ================================================================
        # PONDÉRATION DES DIFFÉRENTS TYPES D'IMPACTS
        # ================================================================

        # 1. IMPACT IMPAIR/PAIR → S/O (poids élevé car direct et exploitable)
        if 'impair_pair_to_so' in cross_impacts and 'impact_strength' in cross_impacts['impair_pair_to_so']:
            impair_pair_strength = cross_impacts['impair_pair_to_so']['impact_strength']
            weight = 3.0  # Poids élevé car très exploitable
            total_weighted_strength += impair_pair_strength * weight
            total_weights += weight

        # 2. IMPACT DESYNC/SYNC → S/O (poids élevé car direct et exploitable)
        if 'desync_sync_to_so' in cross_impacts and 'impact_strength' in cross_impacts['desync_sync_to_so']:
            desync_sync_so_strength = cross_impacts['desync_sync_to_so']['impact_strength']
            weight = 3.0  # Poids élevé car très exploitable
            total_weighted_strength += desync_sync_so_strength * weight
            total_weights += weight

        # 3. IMPACT COMBINÉ → S/O (poids très élevé car synthèse des indices 1-3)
        if 'combined_to_so' in cross_impacts and 'overall_impact_strength' in cross_impacts['combined_to_so']:
            combined_so_strength = cross_impacts['combined_to_so']['overall_impact_strength']
            weight = 4.0  # Poids très élevé car synthèse complète
            total_weighted_strength += combined_so_strength * weight
            total_weights += weight

        # 4. IMPACT DESYNC/SYNC → P/B/T (poids modéré car indirect via S/O)
        if 'desync_sync_to_pbt' in cross_impacts and 'impact_strength' in cross_impacts['desync_sync_to_pbt']:
            desync_sync_pbt_strength = cross_impacts['desync_sync_to_pbt']['impact_strength']
            weight = 2.0  # Poids modéré car indirect
            total_weighted_strength += desync_sync_pbt_strength * weight
            total_weights += weight

        # 5. IMPACT COMBINÉ → P/B/T (poids élevé car synthèse, mais indirect via S/O)
        if 'combined_to_pbt' in cross_impacts and 'overall_impact_strength' in cross_impacts['combined_to_pbt']:
            combined_pbt_strength = cross_impacts['combined_to_pbt']['overall_impact_strength']
            weight = 2.5  # Poids élevé mais indirect
            total_weighted_strength += combined_pbt_strength * weight
            total_weights += weight

        # 6. IMPACTS TRI-DIMENSIONNELS (poids bonus si disponibles)
        if 'tri_dimensional_impacts' in cross_impacts:
            tri_impacts = cross_impacts['tri_dimensional_impacts']
            tri_strength = 0.0
            tri_count = 0

            # Analyser impacts IMPAIR+SYNC
            if 'impair_sync_impacts' in tri_impacts and 'sample_size' in tri_impacts['impair_sync_impacts']:
                if tri_impacts['impair_sync_impacts']['sample_size'] >= 3:
                    tri_strength += self.config.rollout3_quality_bonus_small  # Bonus pour pattern tri-dimensionnel
                    tri_count += 1

            # Analyser impacts PAIR+DESYNC
            if 'pair_desync_impacts' in tri_impacts and 'sample_size' in tri_impacts['pair_desync_impacts']:
                if tri_impacts['pair_desync_impacts']['sample_size'] >= 3:
                    tri_strength += self.config.rollout3_quality_bonus_small  # Bonus pour pattern tri-dimensionnel
                    tri_count += 1

            if tri_count > 0:
                weight = 1.0  # Poids bonus modéré
                total_weighted_strength += (tri_strength / tri_count) * weight
                total_weights += weight

        if total_weights == 0:
            return 0.0

        # Force globale pondérée
        overall_strength = total_weighted_strength / total_weights

        # Bonus pour diversité des impacts (plus d'impacts analysés = plus fiable)
        impact_count = len([k for k in cross_impacts.keys() if k != 'tri_dimensional_impacts'])
        diversity_bonus = min(impact_count / 5.0, 1.2)  # Bonus max 20% avec 5+ impacts

        # Score final avec bonus diversité
        final_strength = overall_strength * diversity_bonus

        return min(final_strength, 1.0)

