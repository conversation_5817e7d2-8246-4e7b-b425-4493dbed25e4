#!/usr/bin/env python3
"""
Extracteur de méthodes depuis class.txt
Crée un fichier séparé pour chaque méthode pour analyse détaillée
"""

import os
import re
from typing import List, Dict, Tuple

class ExtracteurMethodes:
    def __init__(self, fichier_source: str, dossier_sortie: str):
        self.fichier_source = fichier_source
        self.dossier_sortie = dossier_sortie
        self.methodes_extraites = []
        
    def creer_dossier_sortie(self):
        """Crée le dossier de sortie s'il n'existe pas"""
        if not os.path.exists(self.dossier_sortie):
            os.makedirs(self.dossier_sortie)
            print(f"✅ Dossier créé : {self.dossier_sortie}")
        else:
            print(f"📁 Dossier existant : {self.dossier_sortie}")
    
    def lire_fichier_source(self) -> List[str]:
        """Lit le fichier source et retourne les lignes"""
        try:
            with open(self.fichier_source, 'r', encoding='utf-8') as f:
                lignes = f.readlines()
            print(f"✅ Fichier lu : {self.fichier_source} ({len(lignes)} lignes)")
            return lignes
        except Exception as e:
            print(f"❌ Erreur lecture : {e}")
            return []
    
    def identifier_methodes(self, lignes: List[str]) -> List[Dict]:
        """Identifie toutes les méthodes avec leurs positions"""
        methodes = []
        pattern_methode = re.compile(r'^\s*def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(')
        
        for i, ligne in enumerate(lignes):
            match = pattern_methode.match(ligne)
            if match:
                nom_methode = match.group(1)
                methodes.append({
                    'nom': nom_methode,
                    'ligne_debut': i + 1,  # Numérotation à partir de 1
                    'ligne_code': ligne.strip()
                })
        
        print(f"✅ Méthodes identifiées : {len(methodes)}")
        return methodes
    
    def extraire_corps_methode(self, lignes: List[str], ligne_debut: int) -> Tuple[List[str], int]:
        """Extrait le corps complet d'une méthode"""
        corps_methode = []
        indentation_base = None
        ligne_fin = ligne_debut - 1  # Index 0-based
        
        # Ajouter la ligne de définition
        corps_methode.append(lignes[ligne_fin])
        
        # Déterminer l'indentation de base (première ligne non-vide après def)
        for i in range(ligne_fin + 1, len(lignes)):
            ligne = lignes[i]
            if ligne.strip():  # Ligne non vide
                if indentation_base is None:
                    indentation_base = len(ligne) - len(ligne.lstrip())
                
                # Si l'indentation est <= à la base et ce n'est pas un commentaire/docstring
                ligne_indentation = len(ligne) - len(ligne.lstrip())
                if (ligne_indentation <= indentation_base and 
                    not ligne.strip().startswith(('"""', "'''", '#')) and
                    ligne.strip() and
                    i > ligne_fin + 1):  # Pas la première ligne après def
                    # Nouvelle méthode ou fin de classe
                    if ligne.strip().startswith('def ') or ligne.strip().startswith('class '):
                        break
                
                corps_methode.append(ligne)
            else:
                corps_methode.append(ligne)  # Ligne vide
        
        return corps_methode, len(corps_methode)
    
    def sauvegarder_methode(self, methode: Dict, corps: List[str]):
        """Sauvegarde une méthode dans un fichier séparé"""
        nom_fichier = f"{methode['nom']}.txt"
        chemin_fichier = os.path.join(self.dossier_sortie, nom_fichier)
        
        try:
            with open(chemin_fichier, 'w', encoding='utf-8') as f:
                # En-tête informatif
                f.write(f"MÉTHODE : {methode['nom']}\n")
                f.write(f"LIGNE DÉBUT : {methode['ligne_debut']}\n")
                f.write(f"SIGNATURE : {methode['ligne_code']}\n")
                f.write("=" * 80 + "\n\n")
                
                # Corps de la méthode
                for ligne in corps:
                    f.write(ligne)
            
            print(f"✅ Méthode sauvée : {nom_fichier}")
            return True
        except Exception as e:
            print(f"❌ Erreur sauvegarde {nom_fichier} : {e}")
            return False
    
    def extraire_toutes_methodes(self):
        """Processus principal d'extraction"""
        print("🚀 Début extraction des méthodes...")
        
        # 1. Créer dossier de sortie
        self.creer_dossier_sortie()
        
        # 2. Lire fichier source
        lignes = self.lire_fichier_source()
        if not lignes:
            return False
        
        # 3. Identifier toutes les méthodes
        methodes = self.identifier_methodes(lignes)
        if not methodes:
            print("❌ Aucune méthode trouvée")
            return False
        
        # 4. Extraire et sauvegarder chaque méthode
        methodes_reussies = 0
        for i, methode in enumerate(methodes):
            print(f"📝 Extraction {i+1}/{len(methodes)} : {methode['nom']}")
            
            # Extraire le corps de la méthode
            corps, nb_lignes = self.extraire_corps_methode(lignes, methode['ligne_debut'])
            
            # Sauvegarder
            if self.sauvegarder_methode(methode, corps):
                methodes_reussies += 1
                self.methodes_extraites.append({
                    **methode,
                    'nb_lignes': nb_lignes,
                    'fichier': f"{methode['nom']}.txt"
                })
        
        # 5. Rapport final
        print(f"\n🎯 EXTRACTION TERMINÉE")
        print(f"✅ Méthodes extraites : {methodes_reussies}/{len(methodes)}")
        print(f"📁 Dossier de sortie : {self.dossier_sortie}")
        
        return methodes_reussies > 0
    
    def generer_rapport(self):
        """Génère un rapport de l'extraction"""
        if not self.methodes_extraites:
            return
        
        rapport_path = os.path.join(self.dossier_sortie, "RAPPORT_EXTRACTION.txt")
        
        try:
            with open(rapport_path, 'w', encoding='utf-8') as f:
                f.write("RAPPORT D'EXTRACTION DES MÉTHODES\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Source : {self.fichier_source}\n")
                f.write(f"Destination : {self.dossier_sortie}\n")
                f.write(f"Total méthodes : {len(self.methodes_extraites)}\n\n")
                
                f.write("LISTE DES MÉTHODES EXTRAITES :\n")
                f.write("-" * 30 + "\n")
                
                for i, methode in enumerate(self.methodes_extraites, 1):
                    f.write(f"{i:3d}. {methode['nom']:<50} (ligne {methode['ligne_debut']:4d}, {methode['nb_lignes']:3d} lignes)\n")
            
            print(f"📊 Rapport généré : {rapport_path}")
        except Exception as e:
            print(f"❌ Erreur génération rapport : {e}")

def main():
    """Fonction principale"""
    # Configuration
    fichier_source = r"c:\Users\<USER>\Desktop\base\centralisation_methodes\class.txt"
    dossier_sortie = r"c:\Users\<USER>\Desktop\base\methodes_extraites"
    
    # Extraction
    extracteur = ExtracteurMethodes(fichier_source, dossier_sortie)
    
    if extracteur.extraire_toutes_methodes():
        extracteur.generer_rapport()
        print("\n🎉 EXTRACTION RÉUSSIE !")
    else:
        print("\n❌ ÉCHEC DE L'EXTRACTION")

if __name__ == "__main__":
    main()
