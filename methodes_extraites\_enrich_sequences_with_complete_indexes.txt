MÉTHODE : _enrich_sequences_with_complete_indexes
LIGNE DÉBUT : 10034
SIGNATURE : def _enrich_sequences_with_complete_indexes(self, sequences: List[List[Dict]], indices_analysis: Dict, synthesis: Dict) -> List[List[Dict]]:
================================================================================

    def _enrich_sequences_with_complete_indexes(self, sequences: List[List[Dict]], indices_analysis: Dict, synthesis: Dict) -> List[List[Dict]]:
        """
        Enrichit les séquences générées avec l'analyse complète des 5 indices

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Améliore les prédictions en utilisant toute l'intelligence de l'analyse complète

        Args:
            sequences: Liste des 4 séquences générées par les différentes stratégies
            indices_analysis: Analyse complète des 5 indices
            synthesis: Synthèse enrichie avec impacts croisés

        Returns:
            Liste des séquences enrichies avec métriques améliorées
        """

        enriched_sequences = []

        # ================================================================
        # 1. EXTRACTION DES MÉTRIQUES D'ENRICHISSEMENT
        # ================================================================

        # Zones de confiance enrichies
        high_confidence_zones = synthesis.get('high_confidence_zones', [])

        # Impacts croisés
        cross_index_impacts = synthesis.get('cross_index_impacts', {})

        # Variations et évolution temporelle
        variations_impact = synthesis.get('variations_impact', {})

        # Métriques de force globale
        global_strength_metrics = synthesis.get('global_strength_metrics', {})

        # ================================================================
        # 2. ENRICHISSEMENT DE CHAQUE SÉQUENCE
        # ================================================================

        for seq_idx, sequence in enumerate(sequences):
            if not sequence:
                enriched_sequences.append([])
                continue

            enriched_sequence = []

            # Extraire les vraies données de séquence
            if isinstance(sequence, dict) and 'sequence_data' in sequence:
                actual_sequence = sequence['sequence_data']
                strategy_source = sequence.get('strategy', f'strategy_{seq_idx}')
            else:
                actual_sequence = sequence
                strategy_source = f'strategy_{seq_idx}'

            if not actual_sequence:
                enriched_sequences.append([])
                continue

            for hand_idx, hand in enumerate(actual_sequence):
                # Gérer les deux formats : objets dict ou chaînes simples
                if isinstance(hand, dict):
                    enriched_hand = hand.copy()  # Copie de base pour objets
                else:
                    # Créer un objet de base pour les chaînes simples (P, B, T)
                    enriched_hand = {
                        'predicted_pbt': hand,
                        'position_type': 'IMPAIR' if (hand_idx + 1) % 2 == 1 else 'PAIR',
                        'sync_state': 'SYNC',  # Valeur par défaut
                        'pb_confidence': 0.5,
                        'so_confidence': 0.5,
                        'global_confidence': 0.5
                    }

                # ================================================================
                # 2.1. ENRICHISSEMENT BASÉ SUR ZONES DE CONFIANCE
                # ================================================================

                position_type = enriched_hand.get('position_type', 'IMPAIR')
                predicted_pb = enriched_hand.get('predicted_pbt', 'P')
                predicted_so = enriched_hand.get('predicted_so', 'S')
                sync_state = enriched_hand.get('sync_state', 'SYNC')
                combined_state = enriched_hand.get('combined_state', f"{position_type}_{sync_state}")

                # Rechercher zones de confiance applicables
                applicable_zones = []
                for zone in high_confidence_zones:
                    if isinstance(zone, dict) and 'pattern' in zone:
                        zone_pattern = zone.get('pattern', '')

                        # Vérifier applicabilité selon position et état
                        if position_type in zone_pattern or combined_state in zone_pattern:
                            applicable_zones.append(zone)
                        elif f"→{predicted_pb}" in zone_pattern or f"→{predicted_so}" in zone_pattern:
                            applicable_zones.append(zone)

                # Appliquer boost de confiance des zones
                if applicable_zones:
                    best_zone = max(applicable_zones, key=lambda z: z.get('confidence', 0))
                    zone_boost = best_zone.get('confidence', 0) * 0.2  # 20% du boost de zone

                    enriched_hand['pb_confidence'] = min(enriched_hand.get('pb_confidence', 0.5) + zone_boost, 0.95)
                    enriched_hand['so_confidence'] = min(enriched_hand.get('so_confidence', 0.5) + zone_boost, 0.95)
                    enriched_hand['zone_boost_applied'] = zone_boost
                    enriched_hand['best_applicable_zone'] = best_zone.get('type', 'unknown')

                # ================================================================
                # 2.2. ENRICHISSEMENT BASÉ SUR IMPACTS CROISÉS
                # ================================================================

                # Impact IMPAIR/PAIR → P/B
                if 'impair_pair_to_pbt' in cross_index_impacts:
                    impair_pair_pbt = cross_index_impacts['impair_pair_to_pbt']

                    if position_type == 'IMPAIR':
                        impair_to_p = impair_pair_pbt.get('impair_to_player', 0)
                        impair_to_b = impair_pair_pbt.get('impair_to_banker', 0)
                        impair_total = impair_to_p + impair_to_b

                        if impair_total > 0:
                            expected_p_ratio = impair_to_p / impair_total
                            if predicted_pb == 'P' and expected_p_ratio > self.config.rollout2_cross_impact_ratio_threshold:
                                enriched_hand['pb_confidence'] = min(enriched_hand.get('pb_confidence', 0.5) + self.config.rollout2_cross_impact_confidence_boost, self.config.rollout2_cross_impact_max_confidence)
                                enriched_hand['cross_impact_boost'] = 'impair_to_p_strong'
                            elif predicted_pb == 'B' and expected_p_ratio < 0.4:
                                enriched_hand['pb_confidence'] = min(enriched_hand.get('pb_confidence', 0.5) + 0.15, 0.95)
                                enriched_hand['cross_impact_boost'] = 'impair_to_b_strong'

                    elif position_type == 'PAIR':
                        pair_to_p = impair_pair_pbt.get('pair_to_player', 0)
                        pair_to_b = impair_pair_pbt.get('pair_to_banker', 0)
                        pair_total = pair_to_p + pair_to_b

                        if pair_total > 0:
                            expected_p_ratio = pair_to_p / pair_total
                            if predicted_pb == 'P' and expected_p_ratio > self.config.rollout2_cross_impact_ratio_threshold:
                                enriched_hand['pb_confidence'] = min(enriched_hand.get('pb_confidence', 0.5) + self.config.rollout2_cross_impact_confidence_boost, self.config.rollout2_cross_impact_max_confidence)
                                enriched_hand['cross_impact_boost'] = 'pair_to_p_strong'
                            elif predicted_pb == 'B' and expected_p_ratio < 0.4:
                                enriched_hand['pb_confidence'] = min(enriched_hand.get('pb_confidence', 0.5) + 0.15, 0.95)
                                enriched_hand['cross_impact_boost'] = 'pair_to_b_strong'

                # Impact SYNC/DESYNC → S/O
                if 'desync_sync_to_so' in cross_index_impacts:
                    sync_so = cross_index_impacts['desync_sync_to_so']

                    if sync_state == 'SYNC':
                        sync_s_ratio = sync_so.get('sync_to_s_ratio', 0.5)
                        if predicted_so == 'S' and sync_s_ratio > self.config.rollout2_so_bias_strong_threshold:
                            enriched_hand['so_confidence'] = min(enriched_hand.get('so_confidence', 0.5) + 0.2, self.config.rollout2_cross_impact_max_confidence)
                            enriched_hand['sync_so_boost'] = 'sync_to_s_strong'
                        elif predicted_so == 'O' and sync_s_ratio < 0.4:
                            enriched_hand['so_confidence'] = min(enriched_hand.get('so_confidence', 0.5) + 0.2, 0.95)
                            enriched_hand['sync_so_boost'] = 'sync_to_o_strong'

                    elif sync_state == 'DESYNC':
                        desync_s_ratio = sync_so.get('desync_to_s_ratio', 0.5)
                        if predicted_so == 'S' and desync_s_ratio > self.config.rollout2_so_bias_strong_threshold:
                            enriched_hand['so_confidence'] = min(enriched_hand.get('so_confidence', 0.5) + 0.2, self.config.rollout2_cross_impact_max_confidence)
                            enriched_hand['sync_so_boost'] = 'desync_to_s_strong'
                        elif predicted_so == 'O' and desync_s_ratio < 0.4:
                            enriched_hand['so_confidence'] = min(enriched_hand.get('so_confidence', 0.5) + 0.2, 0.95)
                            enriched_hand['sync_so_boost'] = 'desync_to_o_strong'

                # ================================================================
                # 2.3. ENRICHISSEMENT BASÉ SUR ÉVOLUTION TEMPORELLE
                # ================================================================

                if 'temporal_correlation_evolution' in variations_impact:
                    temporal_data = variations_impact['temporal_correlation_evolution']

                    # Phases optimales
                    if 'optimal_prediction_phases' in temporal_data:
                        optimal_phases = temporal_data['optimal_prediction_phases']
                        sequence_length = len(sequence)

                        # Déterminer phase actuelle
                        if hand_idx < sequence_length * self.config.rollout1_phase_early_ratio:
                            current_phase = 'early'
                        elif hand_idx < sequence_length * self.config.rollout1_phase_middle_ratio:
                            current_phase = 'middle'
                        else:
                            current_phase = 'late'

                        # Appliquer boost selon phase optimale
                        best_phase = optimal_phases.get('best_overall_phase', 'unknown')
                        if current_phase in best_phase:
                            phase_boost = optimal_phases.get('correlation_stability', 0) * 0.1
                            enriched_hand['pb_confidence'] = min(enriched_hand.get('pb_confidence', 0.5) + phase_boost, 0.95)
                            enriched_hand['so_confidence'] = min(enriched_hand.get('so_confidence', 0.5) + phase_boost, 0.95)
                            enriched_hand['temporal_phase_boost'] = phase_boost

                # ================================================================
                # 2.4. ENRICHISSEMENT BASÉ SUR FORCE GLOBALE
                # ================================================================

                if global_strength_metrics:
                    overall_strength = global_strength_metrics.get('overall_analysis_strength', 0)

                    if overall_strength > self.config.rollout2_global_strength_high_threshold:  # Force globale élevée
                        strength_boost = (overall_strength - self.config.rollout2_global_strength_high_threshold) * self.config.rollout2_global_strength_boost_factor  # Boost proportionnel
                        enriched_hand['pb_confidence'] = min(enriched_hand.get('pb_confidence', 0.5) + strength_boost, 0.95)
                        enriched_hand['so_confidence'] = min(enriched_hand.get('so_confidence', 0.5) + strength_boost, 0.95)
                        enriched_hand['global_strength_boost'] = strength_boost

                # ================================================================
                # 2.5. RECALCUL CONFIANCE GLOBALE ENRICHIE
                # ================================================================

                # Confiance sync mise à jour
                pb_confidence = enriched_hand.get('pb_confidence', 0.5)
                so_confidence = enriched_hand.get('so_confidence', 0.5)
                sync_confidence = pb_confidence if sync_state == 'SYNC' else (1.0 - pb_confidence)

                # Confiance globale enrichie (P/B poids 2, S/O poids 3)
                enriched_global_confidence = (pb_confidence * 2 + so_confidence * 3) / 5

                # Mise à jour des métriques
                enriched_hand['sync_confidence'] = sync_confidence
                enriched_hand['global_confidence'] = enriched_global_confidence
                enriched_hand['enrichment_applied'] = True
                original_confidence = enriched_hand.get('global_confidence', 0.5) if isinstance(hand, dict) else 0.5
                enriched_hand['original_global_confidence'] = original_confidence
                enriched_hand['confidence_improvement'] = enriched_global_confidence - original_confidence

                enriched_sequence.append(enriched_hand)

            # ================================================================
            # 2.6. MÉTRIQUES DE SÉQUENCE ENRICHIE
            # ================================================================

            if enriched_sequence:
                # Calculer métriques globales de la séquence enrichie
                avg_pb_confidence = sum(h.get('pb_confidence', 0.5) for h in enriched_sequence) / len(enriched_sequence)
                avg_so_confidence = sum(h.get('so_confidence', 0.5) for h in enriched_sequence) / len(enriched_sequence)
                avg_global_confidence = sum(h.get('global_confidence', 0.5) for h in enriched_sequence) / len(enriched_sequence)

                total_improvement = sum(h.get('confidence_improvement', 0) for h in enriched_sequence)

                # Ajouter métrique de synthèse à la fin
                enriched_sequence.append({
                    'type': 'sequence_enrichment_summary',
                    'strategy_source': strategy_source,
                    'sequence_length': len(enriched_sequence) - 1,  # -1 pour exclure cette entrée
                    'avg_pb_confidence': avg_pb_confidence,
                    'avg_so_confidence': avg_so_confidence,
                    'avg_global_confidence': avg_global_confidence,
                    'total_confidence_improvement': total_improvement,
                    'enrichment_effectiveness': total_improvement / max(1, len(enriched_sequence)),
                    'zones_applied': len([h for h in enriched_sequence[:-1] if h.get('zone_boost_applied', 0) > 0]),
                    'cross_impacts_applied': len([h for h in enriched_sequence[:-1] if 'cross_impact_boost' in h]),
                    'temporal_boosts_applied': len([h for h in enriched_sequence[:-1] if 'temporal_phase_boost' in h]),
                    'global_strength_boosts_applied': len([h for h in enriched_sequence[:-1] if 'global_strength_boost' in h])
                })

            enriched_sequences.append(enriched_sequence)

        return enriched_sequences

