ANALYSE DES MÉTHODES CLUSTER 0 ET ROLLOUTS
==================================================

STATISTIQUES :
---------------
Méthodes cluster 0 de base    :  53
Méthodes rollout 1 cluster 0  :  44
Méthodes rollout 2 cluster 0  :  23
Méthodes rollout 3 cluster 0  :  20
TOTAL CLUSTER 0               : 140

Méthodes spécialisées (exclus):  13
Méthodes système (exclus)     :   3

CLUSTER 0 DE BASE :
-----------------
  1. _identify_desync_periods                           (ligne 5654)
  2. _find_consecutive_sequences_with_positions         (ligne 5965)
  3. _find_consecutive_sequences                        (ligne 5996)
  4. _calculate_asymmetric_impair_alert_level           (ligne 6023)
  5. _calculate_asymmetric_pair_alert_level             (ligne 6041)
  6. _calculate_impair_rarity_score                     (ligne 6059)
  7. _calculate_pair_commonality_score                  (ligne 6078)
  8. _calculate_asymmetric_significance                 (ligne 6095)
  9. _identify_dominant_desync_sync_so_pattern          (ligne 6123)
 10. _calculate_combined_so_impact_strength             (ligne 6179)
 11. _calculate_combined_pbt_impact_strength            (ligne 6243)
 12. _identify_dominant_impair_pair_so_pattern          (ligne 6314)
 13. _calculate_overall_impact_strength                 (ligne 6370)
 14. _calculate_distribution                            (ligne 6632)
 15. _calculate_phase_impair_pair_pb_correlation        (ligne 7424)
 16. _calculate_phase_impair_pair_so_correlation        (ligne 7481)
 17. _calculate_phase_sync_desync_pb_correlation        (ligne 7535)
 18. _calculate_phase_sync_desync_so_correlation        (ligne 7592)
 19. _calculate_phase_correlation_strength              (ligne 7646)
 20. _calculate_correlation_stability                   (ligne 7707)
 21. _calculate_variance                                (ligne 7720)
 22. _generate_temporal_recommendation                  (ligne 7731)
 23. _calculate_evolution_strength                      (ligne 7748)
 24. _calculate_temporal_consistency                    (ligne 7764)
 25. _calculate_temporal_predictability                 (ligne 7781)
 26. _calculate_variation_strength_analysis             (ligne 7802)
 27. _extract_consecutive_length_strength               (ligne 7974)
 28. _extract_transition_moments_strength               (ligne 8011)
 29. _extract_desync_periods_strength                   (ligne 8042)
 30. _extract_combined_state_changes_strength           (ligne 8072)
 31. _extract_temporal_evolution_strength               (ligne 8093)
 32. _generate_exploitation_recommendation              (ligne 8125)
 33. _identify_best_prediction_context                  (ligne 8143)
 34. _calculate_strength_distribution                   (ligne 8170)
 35. _calculate_variation_consistency                   (ligne 8187)
 36. _calculate_statistical_significance                (ligne 8229)
 37. _calculate_pattern_stability                       (ligne 8239)
 38. _identify_enhanced_dominant_correlations           (ligne 8268)
 39. _identify_enhanced_high_confidence_zones           (ligne 8566)
 40. _generate_sync_based_sequence                      (ligne 9346)
 41. _generate_combined_index_sequence                  (ligne 9550)
 42. _enrich_sequences_with_complete_indexes            (ligne 10034)
 43. _classify_combined_transition_type                 (ligne 10291)
 44. get_max_sequence_length                            (ligne 10328)
 45. get_max_so_conversions                             (ligne 10343)
 46. is_game_complete                                   (ligne 10358)
 47. _generate_signals_summary                          (ligne 10379)
 48. _generate_generation_guidance                      (ligne 10496)
 49. _generate_quick_access                             (ligne 10601)
 50. _update_performance_metrics                        (ligne 10702)
 51. _count_consecutive_pattern                         (ligne 10721)
 52. _calculate_rupture_probability                     (ligne 10738)
 53. _identify_improbability_zones                      (ligne 10802)

Total : 53 méthodes

ROLLOUT 1 CLUSTER 0 (ANALYSEUR) :
-------------------------------
  1. _rollout_analyzer                                  (ligne  108)
  2. _analyze_impair_consecutive_bias                   (ligne  413)
  3. _analyze_pair_priority_2_autonomous                (ligne  641)
  4. _generate_bias_signals_summary_c2                  (ligne 1328)
  5. _generate_bias_generation_guidance_c2              (ligne 1351)
  6. _generate_bias_quick_access_c2                     (ligne 1374)
  7. _correlate_impair_with_sync                        (ligne 1787)
  8. _correlate_impair_with_combined                    (ligne 1828)
  9. _correlate_impair_with_pb                          (ligne 1870)
 10. _correlate_impair_with_so                          (ligne 1910)
 11. _analyze_combined_structural_bias                  (ligne 2250)
 12. _correlate_bias_to_pb_variations                   (ligne 2374)
 13. _correlate_bias_to_so_variations                   (ligne 2442)
 14. _generate_priority_based_synthesis_autonomous      (ligne 2512)
 15. _generate_bias_exploitation_synthesis              (ligne 2670)
 16. _generate_bias_signals_summary                     (ligne 2835)
 17. _generate_bias_generation_guidance                 (ligne 2851)
 18. _generate_bias_quick_access                        (ligne 2864)
 19. _generate_complete_synthesis                       (ligne 2877)
 20. _calculate_cross_index_impacts                     (ligne 2967)
 21. _calculate_variations_impact                       (ligne 3067)
 22. _calculate_global_strength_metrics                 (ligne 3121)
 23. _analyze_sequence_consistency                      (ligne 3553)
 24. _analyze_complete_impair_pair_index                (ligne 5189)
 25. _analyze_complete_desync_sync_index                (ligne 5259)
 26. _analyze_complete_combined_index                   (ligne 5309)
 27. _analyze_complete_pbt_index                        (ligne 5346)
 28. _analyze_complete_so_index                         (ligne 5394)
 29. _synthesize_complete_analysis                      (ligne 5450)
 30. _analyze_complete_cross_impacts                    (ligne 5507)
 31. _analyze_impair_pair_to_so_impact                  (ligne 5592)
 32. _analyze_desync_sync_to_pbt_impact                 (ligne 5620)
 33. _analyze_desync_sync_to_so_impact                  (ligne 5678)
 34. _analyze_combined_to_pbt_impact                    (ligne 5706)
 35. _analyze_combined_to_so_impact                     (ligne 5750)
 36. _analyze_tri_dimensional_impacts                   (ligne 5786)
 37. _analyze_variations_impact_on_outcomes             (ligne 5828)
 38. _analyze_consecutive_length_impact                 (ligne 5893)
 39. _analyze_transition_moments_impact                 (ligne 6467)
 40. _analyze_desync_periods_impact                     (ligne 6684)
 41. _analyze_combined_state_changes_impact             (ligne 6940)
 42. _analyze_temporal_correlation_evolution            (ligne 7223)
 43. _analyze_correlation_trend                         (ligne 7672)
 44. _analyze_correlations_std_dev                      (ligne 10768)

Total : 44 méthodes

ROLLOUT 2 CLUSTER 0 (GÉNÉRATEUR) :
--------------------------------
  1. _rollout_generator                                 (ligne 3201)
  2. _calculate_sequence_score                          (ligne 3720)
  3. _convert_pb_sequence_to_so                         (ligne 4096)
  4. calculate_rollout2_reward                          (ligne 4178)
  5. calculate_rollout2_sequence_quality                (ligne 4247)
  6. calculate_rollout2_diversity_score                 (ligne 4288)
  7. _define_optimized_generation_space                 (ligne 4531)
  8. _generate_sequences_from_signals                   (ligne 4568)
  9. _generate_sequence_from_signal                     (ligne 4620)
 10. _generate_fallback_sequences                       (ligne 4654)
 11. _classify_confidence_level                         (ligne 4700)
 12. _generate_so_based_sequence                        (ligne 4719)
 13. _generate_all_possible_sequences                   (ligne 4760)
 14. _convert_pb_sequence_to_so_with_history            (ligne 4833)
 15. _calculate_sequence_probability                    (ligne 4861)
 16. _calculate_sequence_quality_metrics                (ligne 4953)
 17. _generate_pb_sequence                              (ligne 5000)
 18. _generate_pair_sync_sequence                       (ligne 5047)
 19. _generate_impair_sync_sequence                     (ligne 5089)
 20. _generate_generic_signal_sequence                  (ligne 5134)
 21. _define_complete_generation_space_DEPRECATED       (ligne 8880)
 22. _generate_impair_pair_optimized_sequence           (ligne 9167)
 23. _generate_so_pattern_sequence                      (ligne 9787)

Total : 23 méthodes

ROLLOUT 3 CLUSTER 0 (PRÉDICTEUR) :
--------------------------------
  1. _rollout_predictor                                 (ligne 3308)
  2. _evaluate_signal_alignment                         (ligne 3473)
  3. _evaluate_fallback_alignment                       (ligne 3529)
  4. _assess_risk_reward_ratio                          (ligne 3613)
  5. _validate_sequence_logic                           (ligne 3659)
  6. _calculate_cluster_confidence_azr_calibrated       (ligne 3833)
  7. _calculate_confidence_risk_factors                 (ligne 3916)
  8. _calculate_epistemic_uncertainty                   (ligne 3975)
  9. _calculate_rollout_consensus                       (ligne 4013)
 10. _get_last_historical_pb_result                     (ligne 4146)
 11. calculate_rollout3_reward                          (ligne 4326)
 12. calculate_rollout3_risk_factor                     (ligne 4414)
 13. calculate_cluster_total_reward                     (ligne 4455)
 14. _calculate_confidence_level                        (ligne 8111)
 15. _assess_sample_size_adequacy                       (ligne 8205)
 16. _assess_overall_quality                            (ligne 8251)
 17. _evaluate_sequence_quality                         (ligne 10849)
 18. _select_best_sequence                              (ligne 10892)
 19. _calculate_cluster_confidence                      (ligne 10906)
 20. _extract_next_hand_prediction                      (ligne 10927)

Total : 20 méthodes

MÉTHODES SPÉCIALISÉES (EXCLUES) :
-------------------------------
  1. execute_cluster_pipeline                           (ligne   29)
  2. _rollout_analyzer_c3_patterns_moyens               (ligne  222)
  3. _analyze_sync_alternation_bias                     (ligne  753)
  4. _rollout_analyzer_c2_patterns_courts               (ligne  909)
  5. _analyze_impair_consecutive_bias_c2_specialized    (ligne 1096)
  6. _analyze_sync_alternation_bias_c2_specialized      (ligne 1207)
  7. _apply_c2_short_patterns_specialization            (ligne 1287)
  8. _get_cluster_specialization_params                 (ligne 1402)
  9. _analyze_impair_bias_specialized                   (ligne 1652)
 10. _analyze_sync_bias_specialized                     (ligne 1700)
 11. _analyze_impair_consecutive_bias_c3_specialized    (ligne 2043)
 12. _analyze_sync_alternation_bias_c3_specialized      (ligne 2128)
 13. _apply_c3_medium_patterns_specialization           (ligne 2209)

Total : 13 méthodes

MÉTHODES SYSTÈME (EXCLUES) :
--------------------------
  1. __init__                                           (ligne    1)
  2. _create_generic_cluster_analyzer                   (ligne 1468)
  3. _apply_cluster_specialization                      (ligne 1745)

Total : 3 méthodes

