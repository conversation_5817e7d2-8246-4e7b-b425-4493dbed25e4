MÉTHODE : _calculate_sequence_quality_metrics
LIGNE DÉBUT : 4953
SIGNATURE : def _calculate_sequence_quality_metrics(self, sequence: List[str], generation_space: Dict) -> Dict:
================================================================================

    def _calculate_sequence_quality_metrics(self, sequence: List[str], generation_space: Dict) -> Dict:
        """
        Calcule des métriques de qualité pour une séquence

        Args:
            sequence: Séquence P/B
            generation_space: Contexte d'analyse

        Returns:
            Dict: Métriques de qualité
        """
        metrics = {}

        # 1. Diversité interne
        unique_count = len(set(sequence))
        metrics['diversity'] = unique_count / len(sequence)

        # 2. Équilibre P/B
        p_count = sequence.count('P')
        b_count = sequence.count('B')
        total = len(sequence)
        balance_score = 1.0 - abs(p_count - b_count) / total
        metrics['balance'] = balance_score

        # 3. Complexité des patterns
        transitions = sum(1 for i in range(1, len(sequence)) if sequence[i] != sequence[i-1])
        metrics['complexity'] = transitions / (len(sequence) - 1) if len(sequence) > 1 else 0

        # 4. Cohérence avec l'état actuel
        current_state = generation_space.get('current_state', 'unknown')
        if 'PAIR' in current_state.upper():
            metrics['state_coherence'] = 0.8 if sequence[0] == 'B' else 0.4
        elif 'IMPAIR' in current_state.upper():
            metrics['state_coherence'] = 0.8 if sequence[0] == 'P' else 0.4
        else:
            metrics['state_coherence'] = 0.5

        # 5. Score global de qualité
        metrics['global_quality'] = (
            metrics['diversity'] * 0.3 +
            metrics['balance'] * 0.25 +
            metrics['complexity'] * 0.2 +
            metrics['state_coherence'] * 0.25
        )

        return metrics

