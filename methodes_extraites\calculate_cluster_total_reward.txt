MÉTHODE : calculate_cluster_total_reward
LIGNE DÉBUT : 4455
SIGNATURE : def calculate_cluster_total_reward(self, rollout1_result: Dict, rollout2_result: Dict, rollout3_result: Dict, actual_outcome: str = None) -> Dict:
================================================================================

    def calculate_cluster_total_reward(self, rollout1_result: Dict, rollout2_result: Dict, rollout3_result: Dict, actual_outcome: str = None) -> Dict:
        """
        Calcule la récompense totale du cluster selon le système AZR

        Args:
            rollout1_result: Résultat du Rollout 1 (analyseur)
            rollout2_result: Résultat du Rollout 2 (générateur)
            rollout3_result: Résultat du Rollout 3 (prédicteur)
            actual_outcome: Résultat réel pour validation (optionnel)

        Returns:
            Dict: Récompense totale avec détails
        """
        # 1. ROLLOUT 1 (Analyseur) - TOUJOURS NEUTRE
        rollout1_reward = self.config.zero_value  # Ni bonus ni malus - Neutralité totale

        # 2. ROLLOUT 2 (Générateur) - Calcul des métriques
        sequences = rollout2_result.get('sequences', [])
        sequence_quality = self.calculate_rollout2_sequence_quality(sequences)
        diversity_score = self.calculate_rollout2_diversity_score(sequences)

        # Facteur de difficulté basé sur l'analyse
        analysis_quality = rollout1_result.get('sequence_metadata', {}).get('analysis_quality', self.config.probability_neutral)
        difficulty_factor = self.config.probability_clamp_max - analysis_quality  # Plus l'analyse est complexe, plus c'est difficile

        rollout2_reward_data = self.calculate_rollout2_reward(sequence_quality, diversity_score, difficulty_factor)
        rollout2_reward = rollout2_reward_data['reward']

        # 3. ROLLOUT 3 (Prédicteur) - Calcul si résultat disponible
        rollout3_reward = self.config.zero_value
        rollout3_reward_data = None

        if actual_outcome and rollout3_result:
            prediction = rollout3_result.get('next_hand_prediction', self.config.rollout2_so_same_result)
            confidence = rollout3_result.get('cluster_confidence', self.config.probability_neutral)
            risk_factor = self.calculate_rollout3_risk_factor(rollout3_result, rollout1_result)

            rollout3_reward_data = self.calculate_rollout3_reward(prediction, actual_outcome, confidence, risk_factor)
            rollout3_reward = rollout3_reward_data['reward']

        # 4. RÉCOMPENSE TOTALE PONDÉRÉE
        rollout1_weight = self.config.cluster_reward_weights['rollout1_weight']  # 0.0
        rollout2_weight = self.config.cluster_reward_weights['rollout2_weight']  # 0.4
        rollout3_weight = self.config.cluster_reward_weights['rollout3_weight']  # 0.6

        total_reward = (
            rollout1_reward * rollout1_weight +     # 0% - Neutralité totale
            rollout2_reward * rollout2_weight +     # 40% - Responsabilité intermédiaire
            rollout3_reward * rollout3_weight       # 60% - Responsabilité maximale
        )

        return {
            'total_reward': total_reward,
            'rollout_contributions': {
                'rollout1_contribution': rollout1_reward * rollout1_weight,
                'rollout2_contribution': rollout2_reward * rollout2_weight,
                'rollout3_contribution': rollout3_reward * rollout3_weight
            },
            'rollout_details': {
                'rollout1_reward': rollout1_reward,
                'rollout2_reward': rollout2_reward,
                'rollout3_reward': rollout3_reward,
                'rollout2_data': rollout2_reward_data,
                'rollout3_data': rollout3_reward_data
            },
            'weights_used': {
                'rollout1_weight': rollout1_weight,
                'rollout2_weight': rollout2_weight,
                'rollout3_weight': rollout3_weight
            }
        }

    # ========================================================================
    # 🎲 MÉTHODES OPTIMISÉES ROLLOUT 2 - GÉNÉRATION BASÉE SIGNAUX
    # ========================================================================

