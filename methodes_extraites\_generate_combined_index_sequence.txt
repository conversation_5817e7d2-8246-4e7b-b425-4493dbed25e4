MÉTHODE : _generate_combined_index_sequence
LIGNE DÉBUT : 9550
SIGNATURE : def _generate_combined_index_sequence(self, generation_space: Dict) -> List[Dict]:
================================================================================

    def _generate_combined_index_sequence(self, generation_space: Dict) -> List[Dict]:
        """
        Génère séquence basée sur exploitation index combiné dominant

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Exploite les patterns d'états combinés (IMPAIR_SYNC, PAIR_DESYNC, etc.) pour optimiser les prédictions

        Args:
            generation_space: Espace de génération complet avec contraintes et guidance

        Returns:
            Liste de mains prédites avec stratégie index combiné optimisée
        """

        sequence_length = self.config.rollout2_fixed_length  # Longueur fixe selon spécifications AZR
        sequence = []

        # ================================================================
        # 1. EXTRACTION DES GUIDANCE COMBINÉES
        # ================================================================

        cross_impact_guidance = generation_space.get('cross_impact_guidance', {})
        confidence_zones = generation_space.get('confidence_zones', {})

        # Guidance impacts combinés → P/B
        combined_pb_guidance = cross_impact_guidance.get('combined_pb', {})

        # Guidance impacts combinés → S/O
        combined_so_guidance = cross_impact_guidance.get('combined_so', {})

        # Zones de confiance combinées
        best_tri_zone = confidence_zones.get('best_tri_zone', {})

        # ================================================================
        # 2. DÉTERMINATION STRATÉGIE COMBINÉE DOMINANTE
        # ================================================================

        # Analyser priorités des guidance
        pb_priority = combined_pb_guidance.get('exploitation_priority', 'MEDIUM')
        so_priority = combined_so_guidance.get('exploitation_priority', 'MEDIUM')
        tri_priority = best_tri_zone.get('exploitability', 'MEDIUM')

        # Déterminer stratégie dominante
        if tri_priority == 'ULTRA_HIGH':
            dominant_strategy = 'TRI_DIMENSIONAL'
            base_confidence = best_tri_zone.get('confidence', 0.7)
        elif pb_priority in ['VERY_HIGH', 'HIGH'] and so_priority in ['VERY_HIGH', 'HIGH']:
            dominant_strategy = 'DUAL_COMBINED'
            base_confidence = self.config.rollout2_base_confidence_high
        elif pb_priority in ['VERY_HIGH', 'HIGH']:
            dominant_strategy = 'PB_FOCUSED'
            base_confidence = combined_pb_guidance.get('strength', 0.7)
        elif so_priority in ['VERY_HIGH', 'HIGH']:
            dominant_strategy = 'SO_FOCUSED'
            base_confidence = combined_so_guidance.get('strength', 0.7)
        else:
            dominant_strategy = 'BALANCED_COMBINED'
            base_confidence = self.config.rollout2_base_confidence_medium

        # ================================================================
        # 3. GÉNÉRATION SÉQUENCE OPTIMISÉE
        # ================================================================

        # Pattern attendu de base (alternance P/B)
        expected_pattern = ['P', 'B']

        # États combinés possibles
        combined_states = ['IMPAIR_SYNC', 'IMPAIR_DESYNC', 'PAIR_SYNC', 'PAIR_DESYNC']

        for i in range(sequence_length):
            hand_number = i + 1
            position_type = 'IMPAIR' if hand_number % 2 == 1 else 'PAIR'

            # ================================================================
            # 3.1. DÉTERMINATION ÉTAT COMBINÉ
            # ================================================================

            # État sync basé sur pattern attendu et stratégie
            expected_outcome = expected_pattern[i % len(expected_pattern)]

            if dominant_strategy == 'TRI_DIMENSIONAL':
                # Exploiter pattern tri-dimensionnel
                tri_pattern = best_tri_zone.get('pattern', '')
                if 'IMPAIR+SYNC' in tri_pattern:
                    if position_type == 'IMPAIR':
                        sync_state = 'SYNC'
                        predicted_pb = expected_outcome
                    else:
                        sync_state = 'DESYNC'
                        predicted_pb = 'B' if expected_outcome == 'P' else 'P'
                else:
                    # Pattern par défaut
                    sync_state = 'SYNC' if i % 2 == 0 else 'DESYNC'
                    predicted_pb = expected_outcome if sync_state == 'SYNC' else ('B' if expected_outcome == 'P' else 'P')

            elif dominant_strategy == 'PB_FOCUSED':
                # Focus sur corrélations P/B
                pb_pattern = combined_pb_guidance.get('pattern', '')
                if 'IMPAIR_SYNC→P' in pb_pattern and position_type == 'IMPAIR':
                    sync_state = 'SYNC'
                    predicted_pb = 'P'
                elif 'PAIR_DESYNC→B' in pb_pattern and position_type == 'PAIR':
                    sync_state = 'DESYNC'
                    predicted_pb = 'B'
                else:
                    # Logique adaptative
                    sync_state = 'SYNC' if position_type == 'IMPAIR' else 'DESYNC'
                    predicted_pb = expected_outcome

            elif dominant_strategy == 'SO_FOCUSED':
                # Focus sur corrélations S/O (détermine sync indirectement)
                so_pattern = combined_so_guidance.get('pattern', '')
                if 'IMPAIR_SYNC→S' in so_pattern and position_type == 'IMPAIR':
                    sync_state = 'SYNC'
                elif 'PAIR_DESYNC→O' in so_pattern and position_type == 'PAIR':
                    sync_state = 'DESYNC'
                else:
                    # Alternance équilibrée
                    sync_state = 'SYNC' if i % 2 == 0 else 'DESYNC'
                predicted_pb = expected_outcome if sync_state == 'SYNC' else ('B' if expected_outcome == 'P' else 'P')

            else:  # DUAL_COMBINED ou BALANCED_COMBINED
                # Stratégie équilibrée
                if i < sequence_length // 3:
                    # Premier tiers : favoriser SYNC
                    sync_state = 'SYNC'
                    predicted_pb = expected_outcome
                elif i < 2 * sequence_length // 3:
                    # Deuxième tiers : alternance
                    sync_state = 'SYNC' if i % 2 == 0 else 'DESYNC'
                    predicted_pb = expected_outcome if sync_state == 'SYNC' else ('B' if expected_outcome == 'P' else 'P')
                else:
                    # Dernier tiers : favoriser DESYNC
                    sync_state = 'DESYNC'
                    predicted_pb = 'B' if expected_outcome == 'P' else 'P'

            # État combiné final
            combined_state = f"{position_type}_{sync_state}"

            # ================================================================
            # 3.2. PRÉDICTION S/O BASÉE SUR ÉTAT COMBINÉ
            # ================================================================

            predicted_so = 'S'  # Défaut
            so_confidence = self.config.rollout3_neutral_evaluation_value  # Confiance de base

            if combined_so_guidance:
                # Exploiter guidance combinée → S/O
                so_pattern = combined_so_guidance.get('pattern', '')
                so_strength = combined_so_guidance.get('strength', 0)

                # Logique basée sur état combiné
                if combined_state == 'IMPAIR_SYNC':
                    if 'IMPAIR_SYNC→S' in so_pattern:
                        predicted_so = 'S'
                    elif 'IMPAIR_SYNC→O' in so_pattern:
                        predicted_so = 'O'
                elif combined_state == 'PAIR_DESYNC':
                    if 'PAIR_DESYNC→S' in so_pattern:
                        predicted_so = 'S'
                    elif 'PAIR_DESYNC→O' in so_pattern:
                        predicted_so = 'O'
                elif combined_state == 'IMPAIR_DESYNC':
                    predicted_so = 'O'  # Tendance DESYNC → O
                elif combined_state == 'PAIR_SYNC':
                    predicted_so = 'S'  # Tendance SYNC → S

                so_confidence = self.config.rollout3_neutral_evaluation_value + so_strength
                so_confidence = min(so_confidence, 0.9)
            else:
                # Logique par défaut basée sur état combiné
                if 'SYNC' in combined_state:
                    predicted_so = 'S'
                    so_confidence = self.config.cluster_base_confidence_medium
                else:
                    predicted_so = 'O'
                    so_confidence = self.config.rollout2_confidence_value_high

            # ================================================================
            # 3.3. CALCUL CONFIANCES
            # ================================================================

            # Confiance P/B basée sur stratégie
            if dominant_strategy == 'TRI_DIMENSIONAL':
                pb_confidence = base_confidence
            elif dominant_strategy == 'PB_FOCUSED':
                pb_confidence = min(base_confidence  + self.config.rollout2_adjustment_small, 0.95)
            elif sync_state == 'SYNC':
                pb_confidence = self.config.cluster_weight_factor_high
            else:
                pb_confidence = self.config.rollout2_confidence_value_medium_high

            # Confiance sync
            sync_confidence = pb_confidence if sync_state == 'SYNC' else (1.0 - pb_confidence)

            # Ajustements selon zones de confiance
            if best_tri_zone and combined_state in best_tri_zone.get('pattern', ''):
                pb_confidence = min(pb_confidence  + self.config.rollout2_adjustment_medium, 0.95)
                so_confidence = min(so_confidence  + self.config.rollout2_adjustment_medium, 0.9)

            # ================================================================
            # 3.4. CONSTRUCTION MAIN PRÉDITE
            # ================================================================

            # Confiance globale pondérée (P/B poids 2, S/O poids 3)
            global_confidence = (pb_confidence * 2 + so_confidence * 3) / 5

            hand = {
                'hand_number': hand_number,
                'position_type': position_type,
                'predicted_pbt': predicted_pb,
                'predicted_so': predicted_so,
                'sync_state': sync_state,
                'combined_state': combined_state,
                'pb_confidence': pb_confidence,
                'so_confidence': so_confidence,
                'sync_confidence': sync_confidence,
                'global_confidence': global_confidence,
                'strategy_source': 'combined_index',
                'dominant_strategy': dominant_strategy,
                'expected_outcome': expected_outcome,
                'guidance_used': {
                    'combined_pb': bool(combined_pb_guidance),
                    'combined_so': bool(combined_so_guidance),
                    'tri_dimensional': bool(best_tri_zone)
                },
                'priorities': {
                    'pb_priority': pb_priority,
                    'so_priority': so_priority,
                    'tri_priority': tri_priority
                }
            }

            sequence.append(hand)

        return sequence

