MÉTHODE : _analyze_complete_combined_index
LIGNE DÉBUT : 5309
SIGNATURE : def _analyze_complete_combined_index(self, impair_pair_data: Dict, desync_sync_data: Dict, hands_data: List) -> Dict:
================================================================================

    def _analyze_complete_combined_index(self, impair_pair_data: Dict, desync_sync_data: Dict, hands_data: List) -> Dict:
        """
        Analyse complète INDEX 3 : COMBINÉ

        Combine les indices IMPAIR/PAIR et DESYNC/SYNC
        """
        combined_analysis = {
            'combined_sequence': [],       # ['IMPAIR_SYNC','PAIR_DESYNC',...]
            'pattern_frequencies': {},     # Fréquences des combinaisons
            'transition_matrix': {},       # Matrice transitions entre états
            'dominant_patterns': [],       # Patterns les plus fréquents
            'correlation_strength': 0.0
        }

        # Création séquence combinée
        for i in range(len(impair_pair_data['position_types'])):
            position_type = impair_pair_data['position_types'][i]
            sync_status = desync_sync_data['sync_sequence'][i]

            combined_state = f"{position_type}_{sync_status}"
            combined_analysis['combined_sequence'].append(combined_state)

        # Calcul fréquences
        unique_states = set(combined_analysis['combined_sequence'])
        for state in unique_states:
            count = combined_analysis['combined_sequence'].count(state)
            combined_analysis['pattern_frequencies'][state] = count / len(combined_analysis['combined_sequence'])

        # Identification patterns dominants
        combined_analysis['dominant_patterns'] = sorted(
            combined_analysis['pattern_frequencies'].items(),
            key=lambda x: x[1],
            reverse=True
        )[:3]  # Top 3

        return combined_analysis

