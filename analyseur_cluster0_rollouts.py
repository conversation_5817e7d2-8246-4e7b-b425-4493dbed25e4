#!/usr/bin/env python3
"""
Analyseur des méthodes du cluster 0 et de ses rollouts 1,2,3
Analyse chaque méthode extraite pour déterminer son appartenance
"""

import os
import re
from typing import List, Dict, Set

class AnalyseurCluster0:
    def __init__(self, dossier_methodes: str):
        self.dossier_methodes = dossier_methodes
        self.methodes_cluster0 = []
        self.methodes_rollout1_c0 = []
        self.methodes_rollout2_c0 = []
        self.methodes_rollout3_c0 = []
        self.methodes_specialisees = []
        self.methodes_systeme = []
        
    def analyser_toutes_methodes(self):
        """Analyse toutes les méthodes extraites"""
        print("🔍 Début analyse des méthodes pour cluster 0...")
        
        # Lister tous les fichiers de méthodes
        fichiers_methodes = [f for f in os.listdir(self.dossier_methodes) 
                           if f.endswith('.txt') and f != 'RAPPORT_EXTRACTION.txt']
        
        print(f"📁 {len(fichiers_methodes)} fichiers de méthodes trouvés")
        
        for fichier in sorted(fichiers_methodes):
            self.analyser_methode(fichier)
        
        self.generer_rapport_analyse()
    
    def analyser_methode(self, nom_fichier: str):
        """Analyse une méthode spécifique"""
        chemin_fichier = os.path.join(self.dossier_methodes, nom_fichier)
        nom_methode = nom_fichier.replace('.txt', '')
        
        try:
            with open(chemin_fichier, 'r', encoding='utf-8') as f:
                contenu = f.read()
            
            # Analyser le contenu pour déterminer l'appartenance
            categorie = self.determiner_categorie_methode(nom_methode, contenu)
            
            methode_info = {
                'nom': nom_methode,
                'fichier': nom_fichier,
                'categorie': categorie,
                'ligne_debut': self.extraire_ligne_debut(contenu)
            }
            
            # Classer selon la catégorie
            if categorie == 'CLUSTER_0':
                self.methodes_cluster0.append(methode_info)
            elif categorie == 'ROLLOUT_1_C0':
                self.methodes_rollout1_c0.append(methode_info)
            elif categorie == 'ROLLOUT_2_C0':
                self.methodes_rollout2_c0.append(methode_info)
            elif categorie == 'ROLLOUT_3_C0':
                self.methodes_rollout3_c0.append(methode_info)
            elif categorie == 'SPECIALISEE':
                self.methodes_specialisees.append(methode_info)
            elif categorie == 'SYSTEME':
                self.methodes_systeme.append(methode_info)
            
            print(f"✅ {nom_methode:<50} → {categorie}")
            
        except Exception as e:
            print(f"❌ Erreur analyse {nom_fichier}: {e}")
    
    def determiner_categorie_methode(self, nom_methode: str, contenu: str) -> str:
        """Détermine la catégorie d'une méthode"""
        
        # 1. MÉTHODES SPÉCIALISÉES (à exclure)
        if self.est_methode_specialisee(nom_methode, contenu):
            return 'SPECIALISEE'
        
        # 2. MÉTHODES SYSTÈME (à exclure)
        if self.est_methode_systeme(nom_methode, contenu):
            return 'SYSTEME'
        
        # 3. ROLLOUTS PRINCIPAUX CLUSTER 0
        if nom_methode == '_rollout_analyzer':
            return 'ROLLOUT_1_C0'
        elif nom_methode == '_rollout_generator':
            return 'ROLLOUT_2_C0'
        elif nom_methode == '_rollout_predictor':
            return 'ROLLOUT_3_C0'
        
        # 4. MÉTHODES PAR PATTERNS DE NOMMAGE
        if self.appartient_rollout1_c0(nom_methode, contenu):
            return 'ROLLOUT_1_C0'
        elif self.appartient_rollout2_c0(nom_methode, contenu):
            return 'ROLLOUT_2_C0'
        elif self.appartient_rollout3_c0(nom_methode, contenu):
            return 'ROLLOUT_3_C0'
        
        # 5. PAR DÉFAUT : CLUSTER 0
        return 'CLUSTER_0'
    
    def est_methode_specialisee(self, nom_methode: str, contenu: str) -> bool:
        """Vérifie si c'est une méthode spécialisée"""
        # Patterns de spécialisation dans le nom
        patterns_specialises = [
            '_c2_', '_c3_', '_c4_', '_c5_', '_c6_', '_c7_',
            'c2_specialized', 'c3_specialized',
            'c2_patterns', 'c3_patterns'
        ]
        
        for pattern in patterns_specialises:
            if pattern in nom_methode:
                return True
        
        # Vérifier dans le contenu
        if 'cluster_id == 2' in contenu or 'cluster_id == 3' in contenu:
            return True
        if 'cluster_id in [2' in contenu or 'cluster_id in [3' in contenu:
            return True
            
        return False
    
    def est_methode_systeme(self, nom_methode: str, contenu: str) -> bool:
        """Vérifie si c'est une méthode système générique"""
        methodes_systeme = [
            '__init__',
            'execute_cluster_pipeline',
            '_get_cluster_specialization_params',
            '_create_generic_cluster_analyzer',
            '_analyze_impair_bias_specialized',
            '_analyze_sync_bias_specialized',
            '_apply_cluster_specialization'
        ]
        
        return nom_methode in methodes_systeme
    
    def appartient_rollout1_c0(self, nom_methode: str, contenu: str) -> bool:
        """Vérifie si la méthode appartient au rollout 1 (analyseur) du cluster 0"""
        patterns_rollout1 = [
            '_analyze_', '_correlate_', '_generate_priority_',
            '_generate_bias_', '_generate_complete_',
            '_calculate_cross_', '_calculate_variations_',
            '_calculate_global_', '_synthesize_'
        ]
        
        for pattern in patterns_rollout1:
            if nom_methode.startswith(pattern):
                return True
        
        return False
    
    def appartient_rollout2_c0(self, nom_methode: str, contenu: str) -> bool:
        """Vérifie si la méthode appartient au rollout 2 (générateur) du cluster 0"""
        patterns_rollout2 = [
            '_define_', '_generate_sequences_', '_generate_sequence_',
            '_generate_fallback_', '_classify_confidence_',
            '_generate_so_', '_generate_all_', '_convert_pb_',
            '_calculate_sequence_', '_generate_pb_',
            '_generate_pair_', '_generate_impair_', '_generate_generic_'
        ]
        
        for pattern in patterns_rollout2:
            if nom_methode.startswith(pattern):
                return True
        
        # Méthodes spécifiques rollout 2
        if nom_methode in ['calculate_rollout2_reward', 'calculate_rollout2_sequence_quality', 
                          'calculate_rollout2_diversity_score']:
            return True
            
        return False
    
    def appartient_rollout3_c0(self, nom_methode: str, contenu: str) -> bool:
        """Vérifie si la méthode appartient au rollout 3 (prédicteur) du cluster 0"""
        patterns_rollout3 = [
            '_evaluate_', '_assess_', '_validate_',
            '_calculate_sequence_score', '_select_best_',
            '_calculate_cluster_confidence', '_calculate_confidence_',
            '_calculate_epistemic_', '_calculate_rollout_',
            '_extract_next_hand_'
        ]
        
        for pattern in patterns_rollout3:
            if nom_methode.startswith(pattern):
                return True
        
        # Méthodes spécifiques rollout 3
        if nom_methode in ['calculate_rollout3_reward', 'calculate_rollout3_risk_factor',
                          'calculate_cluster_total_reward', '_get_last_historical_pb_result']:
            return True
            
        return False
    
    def extraire_ligne_debut(self, contenu: str) -> int:
        """Extrait le numéro de ligne de début depuis l'en-tête"""
        lines = contenu.split('\n')
        for line in lines[:5]:
            if 'LIGNE DÉBUT :' in line:
                try:
                    return int(line.split(':')[1].strip())
                except:
                    pass
        return 0
    
    def generer_rapport_analyse(self):
        """Génère le rapport d'analyse"""
        rapport_path = os.path.join(self.dossier_methodes, "ANALYSE_CLUSTER0_ROLLOUTS.txt")
        
        try:
            with open(rapport_path, 'w', encoding='utf-8') as f:
                f.write("ANALYSE DES MÉTHODES CLUSTER 0 ET ROLLOUTS\n")
                f.write("=" * 50 + "\n\n")
                
                # Statistiques
                total_cluster0 = len(self.methodes_cluster0) + len(self.methodes_rollout1_c0) + \
                               len(self.methodes_rollout2_c0) + len(self.methodes_rollout3_c0)
                
                f.write("STATISTIQUES :\n")
                f.write("-" * 15 + "\n")
                f.write(f"Méthodes cluster 0 de base    : {len(self.methodes_cluster0):3d}\n")
                f.write(f"Méthodes rollout 1 cluster 0  : {len(self.methodes_rollout1_c0):3d}\n")
                f.write(f"Méthodes rollout 2 cluster 0  : {len(self.methodes_rollout2_c0):3d}\n")
                f.write(f"Méthodes rollout 3 cluster 0  : {len(self.methodes_rollout3_c0):3d}\n")
                f.write(f"TOTAL CLUSTER 0               : {total_cluster0:3d}\n\n")
                f.write(f"Méthodes spécialisées (exclus): {len(self.methodes_specialisees):3d}\n")
                f.write(f"Méthodes système (exclus)     : {len(self.methodes_systeme):3d}\n\n")
                
                # Détails par catégorie
                self.ecrire_section_rapport(f, "CLUSTER 0 DE BASE", self.methodes_cluster0)
                self.ecrire_section_rapport(f, "ROLLOUT 1 CLUSTER 0 (ANALYSEUR)", self.methodes_rollout1_c0)
                self.ecrire_section_rapport(f, "ROLLOUT 2 CLUSTER 0 (GÉNÉRATEUR)", self.methodes_rollout2_c0)
                self.ecrire_section_rapport(f, "ROLLOUT 3 CLUSTER 0 (PRÉDICTEUR)", self.methodes_rollout3_c0)
                self.ecrire_section_rapport(f, "MÉTHODES SPÉCIALISÉES (EXCLUES)", self.methodes_specialisees)
                self.ecrire_section_rapport(f, "MÉTHODES SYSTÈME (EXCLUES)", self.methodes_systeme)
            
            print(f"\n📊 Rapport d'analyse généré : {rapport_path}")
            
            # Afficher résumé
            print(f"\n🎯 RÉSULTATS DE L'ANALYSE :")
            print(f"✅ Cluster 0 de base     : {len(self.methodes_cluster0):3d} méthodes")
            print(f"✅ Rollout 1 cluster 0   : {len(self.methodes_rollout1_c0):3d} méthodes")
            print(f"✅ Rollout 2 cluster 0   : {len(self.methodes_rollout2_c0):3d} méthodes")
            print(f"✅ Rollout 3 cluster 0   : {len(self.methodes_rollout3_c0):3d} méthodes")
            print(f"🎯 TOTAL CLUSTER 0       : {total_cluster0:3d} méthodes")
            print(f"❌ Spécialisées (exclus) : {len(self.methodes_specialisees):3d} méthodes")
            print(f"❌ Système (exclus)      : {len(self.methodes_systeme):3d} méthodes")
            
        except Exception as e:
            print(f"❌ Erreur génération rapport : {e}")
    
    def ecrire_section_rapport(self, f, titre: str, methodes: List[Dict]):
        """Écrit une section du rapport"""
        f.write(f"{titre} :\n")
        f.write("-" * len(titre) + "\n")
        
        if not methodes:
            f.write("(Aucune méthode)\n\n")
            return
        
        for i, methode in enumerate(sorted(methodes, key=lambda x: x['ligne_debut']), 1):
            f.write(f"{i:3d}. {methode['nom']:<50} (ligne {methode['ligne_debut']:4d})\n")
        
        f.write(f"\nTotal : {len(methodes)} méthodes\n\n")

def main():
    """Fonction principale"""
    dossier_methodes = r"c:\Users\<USER>\Desktop\base\methodes_extraites"
    
    analyseur = AnalyseurCluster0(dossier_methodes)
    analyseur.analyser_toutes_methodes()
    
    print("\n🎉 ANALYSE TERMINÉE !")

if __name__ == "__main__":
    main()
