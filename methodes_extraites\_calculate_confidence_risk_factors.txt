MÉTHODE : _calculate_confidence_risk_factors
LIGNE DÉBUT : 3916
SIGNATURE : def _calculate_confidence_risk_factors(self, best_sequence: Dict, analyzer_report: Dict) -> float:
================================================================================

    def _calculate_confidence_risk_factors(self, best_sequence: Dict, analyzer_report: Dict) -> float:
        """
        Calcule les facteurs de risque pour la confiance selon TRR++

        Returns:
            float: Score de risque composite (0-1, plus élevé = plus risqué)
        """
        risk_components = []

        # 1. Risque de variance des évaluations
        evaluation = best_sequence.get('evaluation', {})
        scores = [
            evaluation.get('signal_alignment_score', self.config.probability_neutral),
            evaluation.get('consistency_score', self.config.probability_neutral),
            evaluation.get('risk_reward_ratio', self.config.probability_neutral),
            evaluation.get('logic_validation_score', self.config.probability_neutral)
        ]

        import numpy as np
        score_variance = np.var(scores) if len(scores) > 1 else self.config.zero_value
        variance_risk = min(self.config.probability_clamp_max, score_variance * self.config.confidence_calibration['variance_risk_multiplier'])  # Normaliser
        risk_components.append(variance_risk)

        # 2. Risque de faible qualité d'analyse
        synthesis = analyzer_report.get('synthesis', {})
        analysis_quality = synthesis.get('analysis_quality', self.config.probability_neutral)
        analysis_risk = self.config.probability_clamp_max - analysis_quality
        risk_components.append(analysis_risk)

        # 3. Risque de signaux contradictoires
        signals_summary = analyzer_report.get('signals_summary', {})
        top_signals = signals_summary.get('top_signals', [])

        if len(top_signals) >= self.config.two_value:
            # Vérifier la cohérence des signaux
            signal_strengths = [s.get('strength', self.config.probability_neutral) for s in top_signals[:self.config.three_value]]
            signal_variance = np.var(signal_strengths) if len(signal_strengths) > 1 else self.config.zero_value
            signal_risk = min(self.config.probability_clamp_max, signal_variance * self.config.confidence_calibration['signal_risk_multiplier'])
        else:
            signal_risk = self.config.cluster_high_signal_risk  # Risque élevé si peu de signaux

        risk_components.append(signal_risk)

        # 4. Risque de zones de confiance insuffisantes
        high_confidence_zones = synthesis.get('high_confidence_zones', [])
        zone_risk = self.config.probability_clamp_max - min(self.config.probability_clamp_max, len(high_confidence_zones) / self.config.confidence_calibration['optimal_confidence_zones'])  # Optimal selon config
        risk_components.append(zone_risk)

        # Score de risque composite (moyenne pondérée)
        weights = [
            self.config.confidence_calibration['variance_risk_weight'],
            self.config.confidence_calibration['analysis_risk_weight'],
            self.config.confidence_calibration['signal_risk_weight'],
            self.config.confidence_calibration['zone_risk_weight']
        ]
        composite_risk = sum(risk * weight for risk, weight in zip(risk_components, weights))

        return min(self.config.probability_clamp_max, max(self.config.probability_clamp_min, composite_risk))

