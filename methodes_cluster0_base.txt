MÉTHODES DU CLUSTER 0 DE BASE
=============================

Total : 53 méthodes

Ces méthodes constituent le cœur du cluster 0 (cluster par défaut/référence).
Elles ne font partie d'aucun rollout spécialisé mais sont utilisées par 
l'ensemble du système comme base commune.

LISTE COMPLÈTE DES 53 MÉTHODES :
===============================

1.  _identify_desync_periods                           (ligne 5654)
2.  _find_consecutive_sequences_with_positions         (ligne 5965)
3.  _find_consecutive_sequences                        (ligne 5996)
4.  _calculate_asymmetric_impair_alert_level           (ligne 6023)
5.  _calculate_asymmetric_pair_alert_level             (ligne 6041)
6.  _calculate_impair_rarity_score                     (ligne 6059)
7.  _calculate_pair_commonality_score                  (ligne 6078)
8.  _calculate_asymmetric_significance                 (ligne 6095)
9.  _identify_dominant_desync_sync_so_pattern          (ligne 6123)
10. _calculate_combined_so_impact_strength             (ligne 6179)
11. _calculate_combined_pbt_impact_strength            (ligne 6243)
12. _identify_dominant_impair_pair_so_pattern          (ligne 6314)
13. _calculate_overall_impact_strength                 (ligne 6370)
14. _calculate_distribution                            (ligne 6632)
15. _calculate_phase_impair_pair_pb_correlation        (ligne 7424)
16. _calculate_phase_impair_pair_so_correlation        (ligne 7481)
17. _calculate_phase_sync_desync_pb_correlation        (ligne 7535)
18. _calculate_phase_sync_desync_so_correlation        (ligne 7592)
19. _calculate_phase_correlation_strength              (ligne 7646)
20. _calculate_correlation_stability                   (ligne 7707)
21. _calculate_variance                                (ligne 7720)
22. _generate_temporal_recommendation                  (ligne 7731)
23. _calculate_evolution_strength                      (ligne 7748)
24. _calculate_temporal_consistency                    (ligne 7764)
25. _calculate_temporal_predictability                 (ligne 7781)
26. _calculate_variation_strength_analysis             (ligne 7802)
27. _extract_consecutive_length_strength               (ligne 7974)
28. _extract_transition_moments_strength               (ligne 8011)
29. _extract_desync_periods_strength                   (ligne 8042)
30. _extract_combined_state_changes_strength           (ligne 8072)
31. _extract_temporal_evolution_strength               (ligne 8093)
32. _generate_exploitation_recommendation              (ligne 8125)
33. _identify_best_prediction_context                  (ligne 8143)
34. _calculate_strength_distribution                   (ligne 8170)
35. _calculate_variation_consistency                   (ligne 8187)
36. _calculate_statistical_significance                (ligne 8229)
37. _calculate_pattern_stability                       (ligne 8239)
38. _identify_enhanced_dominant_correlations           (ligne 8268)
39. _identify_enhanced_high_confidence_zones           (ligne 8566)
40. _generate_sync_based_sequence                      (ligne 9346)
41. _generate_combined_index_sequence                  (ligne 9550)
42. _enrich_sequences_with_complete_indexes            (ligne 10034)
43. _classify_combined_transition_type                 (ligne 10291)
44. get_max_sequence_length                            (ligne 10328)
45. get_max_so_conversions                             (ligne 10343)
46. is_game_complete                                   (ligne 10358)
47. _generate_signals_summary                          (ligne 10379)
48. _generate_generation_guidance                      (ligne 10496)
49. _generate_quick_access                             (ligne 10601)
50. _update_performance_metrics                        (ligne 10702)
51. _count_consecutive_pattern                         (ligne 10721)
52. _calculate_rupture_probability                     (ligne 10738)
53. _identify_improbability_zones                      (ligne 10802)

CARACTÉRISTIQUES DE CES MÉTHODES :
=================================

1. MÉTHODES UTILITAIRES :
   - get_max_sequence_length
   - get_max_so_conversions  
   - is_game_complete
   - _update_performance_metrics

2. MÉTHODES DE CALCUL STATISTIQUE :
   - _calculate_asymmetric_impair_alert_level
   - _calculate_asymmetric_pair_alert_level
   - _calculate_impair_rarity_score
   - _calculate_pair_commonality_score
   - _calculate_asymmetric_significance
   - _calculate_distribution
   - _calculate_variance
   - _calculate_statistical_significance
   - _calculate_pattern_stability

3. MÉTHODES D'ANALYSE DE CORRÉLATIONS :
   - _calculate_phase_impair_pair_pb_correlation
   - _calculate_phase_impair_pair_so_correlation
   - _calculate_phase_sync_desync_pb_correlation
   - _calculate_phase_sync_desync_so_correlation
   - _calculate_phase_correlation_strength
   - _calculate_correlation_stability

4. MÉTHODES D'IDENTIFICATION DE PATTERNS :
   - _identify_desync_periods
   - _identify_dominant_desync_sync_so_pattern
   - _identify_dominant_impair_pair_so_pattern
   - _identify_enhanced_dominant_correlations
   - _identify_enhanced_high_confidence_zones
   - _identify_best_prediction_context
   - _identify_improbability_zones

5. MÉTHODES DE CALCUL DE FORCE/IMPACT :
   - _calculate_combined_so_impact_strength
   - _calculate_combined_pbt_impact_strength
   - _calculate_overall_impact_strength
   - _calculate_evolution_strength
   - _calculate_variation_strength_analysis
   - _extract_consecutive_length_strength
   - _extract_transition_moments_strength
   - _extract_desync_periods_strength
   - _extract_combined_state_changes_strength
   - _extract_temporal_evolution_strength

6. MÉTHODES DE GÉNÉRATION/RECOMMANDATION :
   - _generate_temporal_recommendation
   - _generate_exploitation_recommendation
   - _generate_sync_based_sequence
   - _generate_combined_index_sequence
   - _generate_signals_summary
   - _generate_generation_guidance
   - _generate_quick_access

7. MÉTHODES DE RECHERCHE DE SÉQUENCES :
   - _find_consecutive_sequences_with_positions
   - _find_consecutive_sequences
   - _count_consecutive_pattern

8. MÉTHODES DE CALCUL DE CONSISTANCE :
   - _calculate_temporal_consistency
   - _calculate_temporal_predictability
   - _calculate_variation_consistency
   - _calculate_strength_distribution

9. MÉTHODES D'ENRICHISSEMENT :
   - _enrich_sequences_with_complete_indexes
   - _classify_combined_transition_type

10. MÉTHODES DE CALCUL DE PROBABILITÉ :
    - _calculate_rupture_probability

Ces 53 méthodes forment la base commune du cluster 0 et sont utilisées
par les rollouts 1, 2, 3 du cluster 0 ainsi que potentiellement par
d'autres parties du système.
