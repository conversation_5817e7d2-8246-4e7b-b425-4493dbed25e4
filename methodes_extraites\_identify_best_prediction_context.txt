MÉTHODE : _identify_best_prediction_context
LIGNE DÉBUT : 8143
SIGNATURE : def _identify_best_prediction_context(self, variations_impact: Dict, dominant_type: str,
================================================================================

    def _identify_best_prediction_context(self, variations_impact: Dict, dominant_type: str,
                                        global_strength: float) -> str:
        """Identifie le contexte optimal pour les prédictions"""

        if global_strength < self.config.rollout1_global_strength_threshold:
            return 'insufficient_patterns'

        # Analyser contexte selon type dominant
        if dominant_type == 'temporal_evolution_strength':
            # Chercher meilleure phase temporelle
            temporal_data = variations_impact.get('temporal_correlation_evolution', {})
            if 'optimal_prediction_phases' in temporal_data:
                best_phase = temporal_data['optimal_prediction_phases'].get('best_overall_phase', 'unknown')
                return f'temporal_phase_{best_phase}'

        elif dominant_type == 'combined_state_changes_strength':
            return 'during_state_transitions'

        elif dominant_type == 'desync_period_strength':
            return 'during_desync_periods'

        elif dominant_type == 'transition_strength':
            return 'at_transition_moments'

        else:
            return 'general_patterns'

