MÉTHODE : _evaluate_fallback_alignment
LIGNE DÉBUT : 3529
SIGNATURE : def _evaluate_fallback_alignment(self, sequence: Dict, analyzer_report: Dict) -> float:
================================================================================

    def _evaluate_fallback_alignment(self, sequence: Dict, analyzer_report: Dict) -> float:
        """
        Évaluation d'alignement fallback basée sur les données détaillées
        """
        alignment_score = 0.5  # Score neutre par défaut

        # Utiliser les corrélations dominantes de la synthèse
        synthesis = analyzer_report.get('synthesis', {})
        dominant_correlations = synthesis.get('dominant_correlations', [])

        sequence_strategy = sequence.get('strategy', '').lower()

        for correlation in dominant_correlations:
            corr_type = correlation.get('type', '')
            corr_strength = correlation.get('strength', 0.0)

            # Vérifier alignement avec corrélations dominantes
            if 'impair' in corr_type.lower() and 'impair' in sequence_strategy:
                alignment_score += corr_strength * self.config.rollout3_quality_bonus_small
            elif 'pair' in corr_type.lower() and 'pair' in sequence_strategy:
                alignment_score += corr_strength * self.config.rollout3_quality_bonus_small

        return min(1.0, alignment_score)

