MÉTHODE : _calculate_distribution
LIGNE DÉBUT : 6632
SIGNATURE : def _calculate_distribution(self, sequence: List[str], possible_values: List[str]) -> Dict:
================================================================================

    def _calculate_distribution(self, sequence: List[str], possible_values: List[str]) -> Dict:
        """
        Calcule la distribution des valeurs dans une séquence

        Méthode utilitaire pour analyses tri-dimensionnelles et statistiques

        Args:
            sequence: Séquence à analyser
            possible_values: Valeurs possibles à compter

        Returns:
            Dictionnaire avec distribution et statistiques
        """

        if not sequence or len(sequence) == 0:
            return {
                'total_count': 0,
                'distributions': {value: 0.0 for value in possible_values},
                'counts': {value: 0 for value in possible_values},
                'dominant_value': None,
                'diversity_score': 0.0
            }

        # Compter occurrences
        counts = {value: sequence.count(value) for value in possible_values}
        total_count = len(sequence)

        # Calculer ratios
        distributions = {value: count / total_count for value, count in counts.items()}

        # Identifier valeur dominante
        dominant_value = max(counts.keys(), key=lambda x: counts[x]) if counts else None

        # Score de diversité (entropie simplifiée)
        diversity_score = 0.0
        for ratio in distributions.values():
            if ratio > 0:
                diversity_score -= ratio * (ratio if ratio > 0 else 0)  # Approximation entropie

        # Normaliser diversité (max = 1.0 pour distribution uniforme)
        max_diversity = len(possible_values) * (1.0 / len(possible_values)) * (1.0 / len(possible_values)) if len(possible_values) > 1 else 0
        normalized_diversity = abs(diversity_score) / max(max_diversity, 0.001)

        return {
            'total_count': total_count,
            'distributions': distributions,
            'counts': counts,
            'dominant_value': dominant_value,
            'dominant_ratio': distributions.get(dominant_value, 0.0) if dominant_value else 0.0,
            'diversity_score': min(normalized_diversity, 1.0)
        }

