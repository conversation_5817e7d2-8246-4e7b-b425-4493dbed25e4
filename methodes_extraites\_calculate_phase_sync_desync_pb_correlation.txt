MÉTHODE : _calculate_phase_sync_desync_pb_correlation
LIGNE DÉBUT : 7535
SIGNATURE : def _calculate_phase_sync_desync_pb_correlation(self, desync_sync_seq: List[str], pbt_seq: List[str]) -> Dict:
================================================================================

    def _calculate_phase_sync_desync_pb_correlation(self, desync_sync_seq: List[str], pbt_seq: List[str]) -> Dict:
        """Calcule corrélations SYNC/DESYNC → P/B pour une phase (sans TIE)"""

        if not desync_sync_seq or not pbt_seq:
            return {'correlation_strength': 0.0, 'sample_size': 0}

        # Filtrer seulement P/B (exclure TIE)
        pb_only = [result for result in pbt_seq if result in ['P', 'B']]

        if len(pb_only) < 2:
            return {'correlation_strength': 0.0, 'sample_size': len(pb_only)}

        # Aligner séquences
        min_length = min(len(desync_sync_seq), len(pb_only))
        aligned_sync = desync_sync_seq[:min_length]
        aligned_pb = pb_only[:min_length]

        # Calculer corrélations SYNC/DESYNC → P/B
        sync_positions = [i for i, val in enumerate(aligned_sync) if val == 'SYNC']
        desync_positions = [i for i, val in enumerate(aligned_sync) if val == 'DESYNC']

        sync_to_p = sum(1 for pos in sync_positions if pos < len(aligned_pb) and aligned_pb[pos] == 'P')
        sync_to_b = sum(1 for pos in sync_positions if pos < len(aligned_pb) and aligned_pb[pos] == 'B')
        desync_to_p = sum(1 for pos in desync_positions if pos < len(aligned_pb) and aligned_pb[pos] == 'P')
        desync_to_b = sum(1 for pos in desync_positions if pos < len(aligned_pb) and aligned_pb[pos] == 'B')

        # Force de corrélation
        sync_total = sync_to_p + sync_to_b
        desync_total = desync_to_p + desync_to_b

        sync_strength = 0.0
        desync_strength = 0.0

        if sync_total > 0:
            sync_p_ratio = sync_to_p / sync_total
            sync_strength = abs(sync_p_ratio - 0.5)

        if desync_total > 0:
            desync_p_ratio = desync_to_p / desync_total
            desync_strength = abs(desync_p_ratio - 0.5)

        # Force globale pondérée
        total_sample = sync_total + desync_total
        if total_sample > 0:
            correlation_strength = (sync_strength * sync_total + desync_strength * desync_total) / total_sample
        else:
            correlation_strength = 0.0

        return {
            'correlation_strength': correlation_strength,
            'sync_strength': sync_strength,
            'desync_strength': desync_strength,
            'sample_size': total_sample,
            'sync_sample': sync_total,
            'desync_sample': desync_total
        }

