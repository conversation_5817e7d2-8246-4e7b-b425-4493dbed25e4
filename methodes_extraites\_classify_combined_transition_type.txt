MÉTHODE : _classify_combined_transition_type
LIGNE DÉBUT : 10291
SIGNATURE : def _classify_combined_transition_type(self, from_state: str, to_state: str) -> str:
================================================================================

    def _classify_combined_transition_type(self, from_state: str, to_state: str) -> str:
        """
        Classifie le type de transition entre deux états combinés

        Args:
            from_state: État de départ (ex: 'IMPAIR_SYNC')
            to_state: État d'arrivée (ex: 'PAIR_DESYNC')

        Returns:
            Type de transition classifié
        """

        # Parser les états
        from_parts = from_state.split('_')
        to_parts = to_state.split('_')

        if len(from_parts) != 2 or len(to_parts) != 2:
            return 'UNKNOWN_TRANSITION'

        from_impair_pair = from_parts[0]  # IMPAIR ou PAIR
        from_sync = from_parts[1]         # SYNC ou DESYNC
        to_impair_pair = to_parts[0]      # IMPAIR ou PAIR
        to_sync = to_parts[1]             # SYNC ou DESYNC

        # Classifier le type de changement
        impair_pair_changed = from_impair_pair != to_impair_pair
        sync_changed = from_sync != to_sync

        if impair_pair_changed and sync_changed:
            return 'COMPLETE_CHANGE'      # Changement complet (ex: IMPAIR_SYNC → PAIR_DESYNC)
        elif impair_pair_changed and not sync_changed:
            return 'IMPAIR_PAIR_CHANGE'   # Changement IMPAIR/PAIR seulement
        elif not impair_pair_changed and sync_changed:
            return 'SYNC_CHANGE'          # Changement SYNC/DESYNC seulement
        else:
            return 'NO_CHANGE'            # Pas de changement (ne devrait pas arriver)

