MÉTHODE : _calculate_pattern_stability
LIGNE DÉBUT : 8239
SIGNATURE : def _calculate_pattern_stability(self, individual_strengths: Dict) -> float:
================================================================================

    def _calculate_pattern_stability(self, individual_strengths: Dict) -> float:
        """Calcule la stabilité des patterns (inverse de la variance)"""

        strengths = list(individual_strengths.values())
        if len(strengths) < 2:
            return 0.0

        variance = self._calculate_variance(strengths)
        stability = 1.0 / (1.0 + variance)  # Normalisation

        return stability

