MÉTHODE : _analyze_impair_pair_to_so_impact
LIGNE DÉBUT : 5592
SIGNATURE : def _analyze_impair_pair_to_so_impact(self, impair_pair_seq: List[str], so_seq: List[str]) -> Dict:
================================================================================

    def _analyze_impair_pair_to_so_impact(self, impair_pair_seq: List[str], so_seq: List[str]) -> Dict:
        """Analyse impact IMPAIR/PAIR → S/O"""
        if len(so_seq) == 0:
            return {'no_data': True}

        # Alignement des séquences (S/O commence à la manche 2)
        aligned_impair_pair = impair_pair_seq[self.config.rollout1_so_start_offset:len(so_seq)+self.config.rollout1_so_start_offset] if len(impair_pair_seq) > len(so_seq) else impair_pair_seq[:len(so_seq)]

        # Calcul corrélations
        impair_s_count = sum(self.config.one_value for ip, so in zip(aligned_impair_pair, so_seq) if ip == 'IMPAIR' and so == 'S')
        impair_o_count = sum(self.config.one_value for ip, so in zip(aligned_impair_pair, so_seq) if ip == 'IMPAIR' and so == 'O')
        pair_s_count = sum(self.config.one_value for ip, so in zip(aligned_impair_pair, so_seq) if ip == 'PAIR' and so == 'S')
        pair_o_count = sum(self.config.one_value for ip, so in zip(aligned_impair_pair, so_seq) if ip == 'PAIR' and so == 'O')

        total_impair = impair_s_count + impair_o_count
        total_pair = pair_s_count + pair_o_count

        return {
            'impair_to_s_ratio': impair_s_count / max(self.config.rollout1_max_divisor_safety, total_impair),
            'impair_to_o_ratio': impair_o_count / max(self.config.rollout1_max_divisor_safety, total_impair),
            'pair_to_s_ratio': pair_s_count / max(self.config.rollout1_max_divisor_safety, total_pair),
            'pair_to_o_ratio': pair_o_count / max(self.config.rollout1_max_divisor_safety, total_pair),
            'dominant_pattern': self._identify_dominant_impair_pair_so_pattern(
                impair_s_count, impair_o_count, pair_s_count, pair_o_count
            ),
            'impact_strength': abs((impair_s_count / max(self.config.rollout1_max_divisor_safety, total_impair)) - (pair_s_count / max(self.config.rollout1_max_divisor_safety, total_pair)))
        }

