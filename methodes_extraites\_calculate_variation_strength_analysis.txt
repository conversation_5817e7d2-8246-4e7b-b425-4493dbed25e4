MÉTHODE : _calculate_variation_strength_analysis
LIGNE DÉBUT : 7802
SIGNATURE : def _calculate_variation_strength_analysis(self, variations_impact: Dict) -> Dict:
================================================================================

    def _calculate_variation_strength_analysis(self, variations_impact: Dict) -> Dict:
        """
        Synthèse finale : calcule la force globale de toutes les variations analysées

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Combine tous les résultats d'analyse des variations en un score global exploitable

        Args:
            variations_impact: Dictionnaire complet avec toutes les analyses de variations

        Returns:
            Dictionnaire avec synthèse finale de la force des variations
        """

        strength_analysis = {
            'global_variation_strength': 0.0,        # Force globale finale (0.0 à 1.0)
            'individual_strengths': {},              # Forces individuelles par type
            'weighted_analysis': {},                 # Analyse pondérée et recommandations
            'variation_quality_metrics': {}          # Métriques de qualité et confiance
        }

        # ================================================================
        # 1. EXTRACTION DES FORCES INDIVIDUELLES
        # ================================================================

        # Force longueurs séquences consécutives
        consecutive_strength = self._extract_consecutive_length_strength(
            variations_impact.get('consecutive_length_impacts', {})
        )

        # Force moments de transition
        transition_strength = self._extract_transition_moments_strength(
            variations_impact.get('transition_impacts', {})
        )

        # Force périodes désynchronisation
        desync_strength = self._extract_desync_periods_strength(
            variations_impact.get('desync_period_impacts', {})
        )

        # Force changements états combinés
        combined_changes_strength = self._extract_combined_state_changes_strength(
            variations_impact.get('combined_state_changes_impacts', {})
        )

        # Force évolution temporelle
        temporal_strength = self._extract_temporal_evolution_strength(
            variations_impact.get('temporal_correlation_evolution', {})
        )

        strength_analysis['individual_strengths'] = {
            'consecutive_length_strength': consecutive_strength,
            'transition_strength': transition_strength,
            'desync_period_strength': desync_strength,
            'combined_state_changes_strength': combined_changes_strength,
            'temporal_evolution_strength': temporal_strength
        }

        # ================================================================
        # 2. PONDÉRATION INTELLIGENTE DES FORCES
        # ================================================================

        # Poids selon importance prédictive (focus S/O > P/B > autres)
        weights = {
            'consecutive_length_strength': 1.0,      # Patterns simples - poids faible
            'transition_strength': 2.0,              # Moments clés - poids moyen
            'desync_period_strength': 2.5,           # Phases importantes - poids moyen+
            'combined_state_changes_strength': 3.0,  # Patterns complexes - poids élevé
            'temporal_evolution_strength': 4.0       # Évolution temporelle - poids max
        }

        # Calcul score global pondéré
        total_weighted_strength = 0.0
        total_weights = 0.0
        valid_strengths = 0

        for strength_type, strength_value in strength_analysis['individual_strengths'].items():
            if strength_value > 0:  # Seulement forces valides
                weight = weights.get(strength_type, 1.0)
                total_weighted_strength += strength_value * weight
                total_weights += weight
                valid_strengths += 1

        # Score global normalisé
        global_strength = total_weighted_strength / total_weights if total_weights > 0 else 0.0
        strength_analysis['global_variation_strength'] = min(global_strength, 1.0)

        # ================================================================
        # 3. ANALYSE PONDÉRÉE ET RECOMMANDATIONS
        # ================================================================

        # Identifier type de variation dominant
        dominant_type = max(
            strength_analysis['individual_strengths'].items(),
            key=lambda x: x[1] * weights.get(x[0], 1.0)
        )[0] if strength_analysis['individual_strengths'] else 'none'

        # Niveau de confiance basé sur force globale
        confidence_level = self._calculate_confidence_level(global_strength, valid_strengths)

        # Recommandation d'exploitation
        exploitation_recommendation = self._generate_exploitation_recommendation(
            global_strength, dominant_type, strength_analysis['individual_strengths']
        )

        # Contexte optimal pour prédictions
        best_context = self._identify_best_prediction_context(
            variations_impact, dominant_type, global_strength
        )

        strength_analysis['weighted_analysis'] = {
            'dominant_variation_type': dominant_type,
            'confidence_level': confidence_level,
            'exploitation_recommendation': exploitation_recommendation,
            'best_prediction_context': best_context,
            'valid_variation_types': valid_strengths,
            'strength_distribution': self._calculate_strength_distribution(
                strength_analysis['individual_strengths'], weights
            )
        }

        # ================================================================
        # 4. MÉTRIQUES DE QUALITÉ ET CONFIANCE
        # ================================================================

        # Consistance entre types de variations
        consistency = self._calculate_variation_consistency(
            strength_analysis['individual_strengths']
        )

        # Adéquation des échantillons
        sample_adequacy = self._assess_sample_size_adequacy(variations_impact)

        # Significativité statistique
        statistical_significance = self._calculate_statistical_significance(
            strength_analysis['individual_strengths'], variations_impact
        )

        # Stabilité des patterns (variance faible = plus stable)
        pattern_stability = self._calculate_pattern_stability(
            strength_analysis['individual_strengths']
        )

        strength_analysis['variation_quality_metrics'] = {
            'consistency_across_types': consistency,
            'sample_size_adequacy': sample_adequacy,
            'statistical_significance': statistical_significance,
            'pattern_stability': pattern_stability,
            'overall_quality_score': (consistency + sample_adequacy + statistical_significance + pattern_stability) / 4,
            'quality_assessment': self._assess_overall_quality(
                consistency, sample_adequacy, statistical_significance, pattern_stability
            )
        }

        # ================================================================
        # 5. MÉTRIQUES FINALES ET VALIDATION
        # ================================================================

        # Validation cohérence globale
        strength_analysis['validation'] = {
            'has_significant_variations': global_strength > 0.3,  # Seuil 30%
            'has_high_confidence': confidence_level in ['HIGH', 'VERY_HIGH'],
            'is_exploitable': global_strength > 0.5 and consistency > 0.6,
            'recommended_for_predictions': (
                global_strength > self.config.rollout2_global_strength_recommendation and
                statistical_significance > self.config.rollout2_statistical_significance_threshold and
                valid_strengths >= 3
            )
        }

        return strength_analysis

