MÉTHODE : _calculate_variance
LIGNE DÉBUT : 7720
SIGNATURE : def _calculate_variance(self, values: List[float]) -> float:
================================================================================

    def _calculate_variance(self, values: List[float]) -> float:
        """Calcule la variance d'une liste de valeurs"""

        if len(values) < 2:
            return 0.0

        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)

        return variance

