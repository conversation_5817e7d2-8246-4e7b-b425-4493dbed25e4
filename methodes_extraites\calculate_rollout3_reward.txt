MÉTHODE : calculate_rollout3_reward
LIGNE DÉBUT : 4326
SIGNATURE : def calculate_rollout3_reward(self, prediction: str, actual_outcome: str, confidence: float, risk_factor: float) -> Dict:
================================================================================

    def calculate_rollout3_reward(self, prediction: str, actual_outcome: str, confidence: float, risk_factor: float) -> Dict:
        """
        Calcule la récompense du Rollout 3 selon les formules TRR++ et AZR

        Basé sur :
        - Récompense de correctness binaire
        - Bonus/malus de confiance calibrée (TRR++)
        - Facteur de difficulté relative (zone proximale)
        - Malus de sur-confiance (innovation AZR)

        Args:
            prediction: Prédiction du rollout ('S' ou 'O')
            actual_outcome: Résultat réel ('S' ou 'O')
            confidence: Niveau de confiance (0-1)
            risk_factor: Facteur de risque de la prédiction (0-1)

        Returns:
            Dict: Récompense détaillée avec composants
        """
        # 1. RÉCOMPENSE DE BASE (Correctness binaire)
        is_correct = (prediction == actual_outcome)
        # ⚠️ UTILISATION DES PARAMÈTRES CENTRALISÉS AZRConfig
        base_reward = self.config.correlation_player_value if is_correct else self.config.default_return_value

        # 2. BONUS/MALUS DE CONFIANCE CALIBRÉE (Formule TRR++)
        confidence_adjustment = self.config.default_return_value

        if is_correct:
            # Prédiction correcte : bonus pour confiance justifiée
            confidence_bonus_correct = self.config.rollout3_rewards['confidence_bonus_correct']
            confidence_adjustment = confidence * confidence_bonus_correct  # Max +20%
        else:
            # Prédiction incorrecte : bonus pour humilité
            confidence_bonus_incorrect = self.config.rollout3_rewards['confidence_bonus_incorrect']
            confidence_adjustment = (self.config.correlation_player_value - confidence) * confidence_bonus_incorrect  # Max +10%

        # 3. FACTEUR DE DIFFICULTÉ RELATIVE (Zone proximale)
        difficulty_bonus = self.config.default_return_value
        optimal_risk = self.config.rollout3_rewards['optimal_risk']
        min_risk = self.config.rollout3_rewards['min_risk']
        max_risk = self.config.rollout3_rewards['max_risk']
        difficulty_bonus_max = self.config.rollout3_rewards['difficulty_bonus_max']

        if min_risk <= risk_factor <= max_risk:  # Zone proximale AZR
            distance = abs(risk_factor - optimal_risk)
            # ⚠️ UTILISATION DES PARAMÈTRES CENTRALISÉS AZRConfig
            difficulty_bonus = (self.config.correlation_player_value - distance / self.config.frequency_expected_rare) * difficulty_bonus_max  # Max +10%

        # 4. MALUS DE SUR-CONFIANCE (Innovation AZR)
        overconfidence_penalty = self.config.default_return_value
        overconfidence_threshold = self.config.rollout3_rewards['overconfidence_threshold']
        overconfidence_penalty_max = self.config.rollout3_rewards['overconfidence_penalty_max']

        if not is_correct and confidence > overconfidence_threshold:
            # Pénalité pour sur-confiance sur erreur
            # ⚠️ UTILISATION DES PARAMÈTRES CENTRALISÉS AZRConfig
            overconfidence_penalty = (confidence - overconfidence_threshold) * (overconfidence_penalty_max / self.config.veto_impairs_weak_threshold)  # Max -10%

        # 5. RÉCOMPENSE FINALE
        final_reward = (
            base_reward +
            confidence_adjustment +
            difficulty_bonus -
            overconfidence_penalty
        )

        # Borner entre min et max
        min_reward = self.config.rollout3_rewards['min_reward']
        max_reward = self.config.rollout3_rewards['max_reward']
        final_reward = max(min_reward, min(max_reward, final_reward))

        return {
            'reward': final_reward,
            'components': {
                'base_reward': base_reward,
                'confidence_adjustment': confidence_adjustment,
                'difficulty_bonus': difficulty_bonus,
                'overconfidence_penalty': overconfidence_penalty
            },
            'analysis': {
                'is_correct': is_correct,
                'confidence': confidence,
                'risk_factor': risk_factor,
                'in_proximal_zone': min_risk <= risk_factor <= max_risk,
                'overconfident': not is_correct and confidence > overconfidence_threshold
            }
        }

