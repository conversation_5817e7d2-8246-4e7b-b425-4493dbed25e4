MÉTHODE : _calculate_strength_distribution
LIGNE DÉBUT : 8170
SIGNATURE : def _calculate_strength_distribution(self, individual_strengths: Dict, weights: Dict) -> Dict:
================================================================================

    def _calculate_strength_distribution(self, individual_strengths: Dict, weights: Dict) -> Dict:
        """Calcule la distribution pondérée des forces"""

        total_weighted = sum(strength * weights.get(strength_type, 1.0)
                           for strength_type, strength in individual_strengths.items())

        if total_weighted == 0:
            return {}

        distribution = {}
        for strength_type, strength in individual_strengths.items():
            weight = weights.get(strength_type, 1.0)
            weighted_strength = strength * weight
            distribution[strength_type] = weighted_strength / total_weighted

        return distribution

