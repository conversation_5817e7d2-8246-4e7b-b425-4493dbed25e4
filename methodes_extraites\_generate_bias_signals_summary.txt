MÉTHODE : _generate_bias_signals_summary
LIGNE DÉBUT : 2835
SIGNATURE : def _generate_bias_signals_summary(self, bias_synthesis: Dict) -> Dict:
================================================================================

    def _generate_bias_signals_summary(self, bias_synthesis: Dict) -> Dict:
        """Génère le résumé des signaux de biais pour le Rollout 2"""
        return {
            'top_bias_signals': [
                {
                    'signal_name': bias_synthesis.get('strongest_bias', {}).get('bias_type', 'unknown'),
                    'strength': bias_synthesis.get('strongest_bias', {}).get('bias_strength', 0.0),
                    'confidence': bias_synthesis.get('exploitation_confidence', 0.0),
                    'exploitation_ready': bias_synthesis.get('strongest_bias', {}).get('exploitation_ready', False)
                }
            ],
            'overall_exploitation_quality': bias_synthesis.get('exploitation_quality', 0.0),
            'bias_persistence_score': bias_synthesis.get('bias_persistence', 0.0),
            'exploitation_strategy': bias_synthesis.get('optimal_exploitation_strategy', {}).get('strategy_type', 'NO_EXPLOITATION')
        }

