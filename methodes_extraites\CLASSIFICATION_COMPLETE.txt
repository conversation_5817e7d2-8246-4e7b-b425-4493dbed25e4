CLASSIFICATION COMPLÈTE DES 162 MÉTHODES
==================================================

STATISTIQUES GLOBALES :
-------------------------
Total méthodes analysées : 156

CLUSTER 0 :
-----------
Total méthodes cluster 0 : 141

  ROLLOUT 1 (45 méthodes) :
      1. _rollout_analyzer                                  (ligne  108)
      2. _analyze_impair_consecutive_bias                   (ligne  413)
      3. _analyze_pair_priority_2_autonomous                (ligne  641)
      4. _analyze_sync_alternation_bias                     (ligne  753)
      5. _generate_bias_signals_summary_c2                  (ligne 1328)
      6. _generate_bias_generation_guidance_c2              (ligne 1351)
      7. _generate_bias_quick_access_c2                     (ligne 1374)
      8. _correlate_impair_with_sync                        (ligne 1787)
      9. _correlate_impair_with_combined                    (ligne 1828)
     10. _correlate_impair_with_pb                          (ligne 1870)
     11. _correlate_impair_with_so                          (ligne 1910)
     12. _analyze_combined_structural_bias                  (ligne 2250)
     13. _correlate_bias_to_pb_variations                   (ligne 2374)
     14. _correlate_bias_to_so_variations                   (ligne 2442)
     15. _generate_priority_based_synthesis_autonomous      (ligne 2512)
     16. _generate_bias_exploitation_synthesis              (ligne 2670)
     17. _generate_bias_signals_summary                     (ligne 2835)
     18. _generate_bias_generation_guidance                 (ligne 2851)
     19. _generate_bias_quick_access                        (ligne 2864)
     20. _generate_complete_synthesis                       (ligne 2877)
     21. _calculate_cross_index_impacts                     (ligne 2967)
     22. _calculate_variations_impact                       (ligne 3067)
     23. _calculate_global_strength_metrics                 (ligne 3121)
     24. _analyze_sequence_consistency                      (ligne 3553)
     25. _analyze_complete_impair_pair_index                (ligne 5189)
     26. _analyze_complete_desync_sync_index                (ligne 5259)
     27. _analyze_complete_combined_index                   (ligne 5309)
     28. _analyze_complete_pbt_index                        (ligne 5346)
     29. _analyze_complete_so_index                         (ligne 5394)
     30. _synthesize_complete_analysis                      (ligne 5450)
     31. _analyze_complete_cross_impacts                    (ligne 5507)
     32. _analyze_impair_pair_to_so_impact                  (ligne 5592)
     33. _analyze_desync_sync_to_pbt_impact                 (ligne 5620)
     34. _analyze_desync_sync_to_so_impact                  (ligne 5678)
     35. _analyze_combined_to_pbt_impact                    (ligne 5706)
     36. _analyze_combined_to_so_impact                     (ligne 5750)
     37. _analyze_tri_dimensional_impacts                   (ligne 5786)
     38. _analyze_variations_impact_on_outcomes             (ligne 5828)
     39. _analyze_consecutive_length_impact                 (ligne 5893)
     40. _analyze_transition_moments_impact                 (ligne 6467)
     41. _analyze_desync_periods_impact                     (ligne 6684)
     42. _analyze_combined_state_changes_impact             (ligne 6940)
     43. _analyze_temporal_correlation_evolution            (ligne 7223)
     44. _analyze_correlation_trend                         (ligne 7672)
     45. _analyze_correlations_std_dev                      (ligne 10768)

  ROLLOUT 2 (30 méthodes) :
      1. _rollout_generator                                 (ligne 3201)
      2. _calculate_sequence_score                          (ligne 3720)
      3. _convert_pb_sequence_to_so                         (ligne 4096)
      4. _get_last_historical_pb_result                     (ligne 4146)
      5. calculate_rollout2_reward                          (ligne 4178)
      6. calculate_rollout2_sequence_quality                (ligne 4247)
      7. calculate_rollout2_diversity_score                 (ligne 4288)
      8. _define_optimized_generation_space                 (ligne 4531)
      9. _generate_sequences_from_signals                   (ligne 4568)
     10. _generate_sequence_from_signal                     (ligne 4620)
     11. _generate_fallback_sequences                       (ligne 4654)
     12. _classify_confidence_level                         (ligne 4700)
     13. _generate_so_based_sequence                        (ligne 4719)
     14. _generate_all_possible_sequences                   (ligne 4760)
     15. _convert_pb_sequence_to_so_with_history            (ligne 4833)
     16. _calculate_sequence_probability                    (ligne 4861)
     17. _calculate_sequence_quality_metrics                (ligne 4953)
     18. _generate_pb_sequence                              (ligne 5000)
     19. _generate_pair_sync_sequence                       (ligne 5047)
     20. _generate_impair_sync_sequence                     (ligne 5089)
     21. _generate_generic_signal_sequence                  (ligne 5134)
     22. _define_complete_generation_space_DEPRECATED       (ligne 8880)
     23. _generate_impair_pair_optimized_sequence           (ligne 9167)
     24. _generate_sync_based_sequence                      (ligne 9346)
     25. _generate_combined_index_sequence                  (ligne 9550)
     26. _generate_so_pattern_sequence                      (ligne 9787)
     27. is_game_complete                                   (ligne 10358)
     28. _generate_signals_summary                          (ligne 10379)
     29. _generate_generation_guidance                      (ligne 10496)
     30. _generate_quick_access                             (ligne 10601)

  ROLLOUT 3 (19 méthodes) :
      1. _rollout_predictor                                 (ligne 3308)
      2. _evaluate_signal_alignment                         (ligne 3473)
      3. _evaluate_fallback_alignment                       (ligne 3529)
      4. _assess_risk_reward_ratio                          (ligne 3613)
      5. _validate_sequence_logic                           (ligne 3659)
      6. _calculate_cluster_confidence_azr_calibrated       (ligne 3833)
      7. _calculate_confidence_risk_factors                 (ligne 3916)
      8. _calculate_epistemic_uncertainty                   (ligne 3975)
      9. _calculate_rollout_consensus                       (ligne 4013)
     10. calculate_rollout3_reward                          (ligne 4326)
     11. calculate_rollout3_risk_factor                     (ligne 4414)
     12. calculate_cluster_total_reward                     (ligne 4455)
     13. _calculate_confidence_level                        (ligne 8111)
     14. _assess_sample_size_adequacy                       (ligne 8205)
     15. _assess_overall_quality                            (ligne 8251)
     16. _evaluate_sequence_quality                         (ligne 10849)
     17. _select_best_sequence                              (ligne 10892)
     18. _calculate_cluster_confidence                      (ligne 10906)
     19. _extract_next_hand_prediction                      (ligne 10927)

  ROLLOUT BASE (47 méthodes) :
      1. _identify_desync_periods                           (ligne 5654)
      2. _find_consecutive_sequences_with_positions         (ligne 5965)
      3. _find_consecutive_sequences                        (ligne 5996)
      4. _calculate_asymmetric_impair_alert_level           (ligne 6023)
      5. _calculate_asymmetric_pair_alert_level             (ligne 6041)
      6. _calculate_impair_rarity_score                     (ligne 6059)
      7. _calculate_pair_commonality_score                  (ligne 6078)
      8. _calculate_asymmetric_significance                 (ligne 6095)
      9. _identify_dominant_desync_sync_so_pattern          (ligne 6123)
     10. _calculate_combined_so_impact_strength             (ligne 6179)
     11. _calculate_combined_pbt_impact_strength            (ligne 6243)
     12. _identify_dominant_impair_pair_so_pattern          (ligne 6314)
     13. _calculate_overall_impact_strength                 (ligne 6370)
     14. _calculate_distribution                            (ligne 6632)
     15. _calculate_phase_impair_pair_pb_correlation        (ligne 7424)
     16. _calculate_phase_impair_pair_so_correlation        (ligne 7481)
     17. _calculate_phase_sync_desync_pb_correlation        (ligne 7535)
     18. _calculate_phase_sync_desync_so_correlation        (ligne 7592)
     19. _calculate_phase_correlation_strength              (ligne 7646)
     20. _calculate_correlation_stability                   (ligne 7707)
     21. _calculate_variance                                (ligne 7720)
     22. _generate_temporal_recommendation                  (ligne 7731)
     23. _calculate_evolution_strength                      (ligne 7748)
     24. _calculate_temporal_consistency                    (ligne 7764)
     25. _calculate_temporal_predictability                 (ligne 7781)
     26. _calculate_variation_strength_analysis             (ligne 7802)
     27. _extract_consecutive_length_strength               (ligne 7974)
     28. _extract_transition_moments_strength               (ligne 8011)
     29. _extract_desync_periods_strength                   (ligne 8042)
     30. _extract_combined_state_changes_strength           (ligne 8072)
     31. _extract_temporal_evolution_strength               (ligne 8093)
     32. _generate_exploitation_recommendation              (ligne 8125)
     33. _identify_best_prediction_context                  (ligne 8143)
     34. _calculate_strength_distribution                   (ligne 8170)
     35. _calculate_variation_consistency                   (ligne 8187)
     36. _calculate_statistical_significance                (ligne 8229)
     37. _calculate_pattern_stability                       (ligne 8239)
     38. _identify_enhanced_dominant_correlations           (ligne 8268)
     39. _identify_enhanced_high_confidence_zones           (ligne 8566)
     40. _enrich_sequences_with_complete_indexes            (ligne 10034)
     41. _classify_combined_transition_type                 (ligne 10291)
     42. get_max_sequence_length                            (ligne 10328)
     43. get_max_so_conversions                             (ligne 10343)
     44. _update_performance_metrics                        (ligne 10702)
     45. _count_consecutive_pattern                         (ligne 10721)
     46. _calculate_rupture_probability                     (ligne 10738)
     47. _identify_improbability_zones                      (ligne 10802)


CLUSTER 2 :
-----------
Total méthodes cluster 2 : 4

  ROLLOUT 1 (3 méthodes) :
      1. _rollout_analyzer_c2_patterns_courts               (ligne  909)
      2. _analyze_impair_consecutive_bias_c2_specialized    (ligne 1096)
      3. _analyze_sync_alternation_bias_c2_specialized      (ligne 1207)

  ROLLOUT BASE (1 méthodes) :
      1. _apply_c2_short_patterns_specialization            (ligne 1287)


CLUSTER 3 :
-----------
Total méthodes cluster 3 : 4

  ROLLOUT 1 (3 méthodes) :
      1. _rollout_analyzer_c3_patterns_moyens               (ligne  222)
      2. _analyze_impair_consecutive_bias_c3_specialized    (ligne 2043)
      3. _analyze_sync_alternation_bias_c3_specialized      (ligne 2128)

  ROLLOUT BASE (1 méthodes) :
      1. _apply_c3_medium_patterns_specialization           (ligne 2209)


CLUSTER SYSTÈME :
-----------------
Total méthodes cluster SYSTÈME : 7

  ROLLOUT SYSTÈME (7 méthodes) :
      1. __init__                                           (ligne    1)
      2. execute_cluster_pipeline                           (ligne   29)
      3. _get_cluster_specialization_params                 (ligne 1402)
      4. _create_generic_cluster_analyzer                   (ligne 1468)
      5. _analyze_impair_bias_specialized                   (ligne 1652)
      6. _analyze_sync_bias_specialized                     (ligne 1700)
      7. _apply_cluster_specialization                      (ligne 1745)


RÉSUMÉ CLUSTER 0 (OBJECTIF PRINCIPAL) :
========================================
Total méthodes cluster 0 : 141
  - Rollout 1 : 45 méthodes
  - Rollout 2 : 30 méthodes
  - Rollout 3 : 19 méthodes
  - Rollout BASE : 47 méthodes

Ces 141 méthodes constituent l'ensemble complet
du cluster 0 et de ses rollouts 1, 2, 3.
