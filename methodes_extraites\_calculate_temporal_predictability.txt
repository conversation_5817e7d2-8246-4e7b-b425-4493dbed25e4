MÉTHODE : _calculate_temporal_predictability
LIGNE DÉBUT : 7781
SIGNATURE : def _calculate_temporal_predictability(self, trends: Dict) -> float:
================================================================================

    def _calculate_temporal_predictability(self, trends: Dict) -> float:
        """Calcule la prédictibilité temporelle des corrélations"""

        if not trends:
            return 0.0

        # Prédictibilité basée sur la clarté des tendances
        clear_trends = 0
        total_trends = 0

        for trend_data in trends.values():
            total_trends += 1
            trend_type = trend_data.get('trend_type', 'UNKNOWN')

            if trend_type in ['STRENGTHENING', 'WEAKENING', 'PEAK_THEN_DECLINE']:
                trend_strength = trend_data.get('trend_strength', 0.0)
                if trend_strength > self.config.rollout2_signal_strength_threshold:  # Seuil de significativité
                    clear_trends += 1

        return clear_trends / total_trends if total_trends > 0 else 0.0

