📋 MÉTHODES CLUSTER PAR DÉFAUT - ANALYSE DIRECTE class.txt
================================================================================

🎯 ANALYSE BASÉE SUR SOURCE PRIMAIRE : class.txt (10,953 lignes)
📅 Date d'analyse : Décembre 2024
🔍 Méthode : Recherche directe des méthodes avec pattern "def _.*\(self"
🎯 Focus : Cluster par défaut (0-1) et ses 3 rollouts

================================================================================
📋 RÉSUMÉ EXÉCUTIF
================================================================================

✅ TOTAL MÉTHODES TROUVÉES : 152 méthodes dans class.txt
🎯 CLUSTER PAR DÉFAUT : Méthodes pour cluster 0-1 (référence)
🔧 ROLLOUTS : 3 rollouts par cluster (Analyseur, Générateur, Prédicteur)
📊 STATUT : Anciennes méthodes spécialisées (remplacées par versions universelles)

================================================================================
🎯 ROLLOUT 1 - ANALYSEUR (CLUSTER PAR DÉFAUT)
================================================================================

MÉTHODE PRINCIPALE :
1. _rollout_analyzer (ligne 108)
   📝 Rollout 1 Analyseur de Biais - Exploitation contraintes structurelles
   🎯 Version cluster par défaut (0-1)
   ⚠️ REMPLACÉE par version universelle dans azr_baccarat_predictor.py

MÉTHODES SUPPORT ROLLOUT 1 (47 méthodes) :

ANALYSE DES BIAIS :
2. _analyze_impair_consecutive_bias (ligne 413)
3. _analyze_pair_priority_2_autonomous (ligne 641)
4. _analyze_sync_alternation_bias (ligne 753)
5. _analyze_combined_structural_bias (ligne 2250)

CORRÉLATIONS :
6. _correlate_impair_with_sync (ligne 1787)
7. _correlate_impair_with_combined (ligne 1828)
8. _correlate_impair_with_pb (ligne 1870)
9. _correlate_impair_with_so (ligne 1910)
10. _correlate_bias_to_pb_variations (ligne 1950, 2374)
11. _correlate_bias_to_so_variations (ligne 2001, 2442)

SYNTHÈSES :
12. _generate_priority_based_synthesis_autonomous (ligne 2512)
13. _generate_bias_exploitation_synthesis (ligne 2670)
14. _generate_bias_signals_summary (ligne 2835)
15. _generate_bias_generation_guidance (ligne 2851)
16. _generate_bias_quick_access (ligne 2864)

ANALYSES COMPLÈTES DES 5 INDICES :
17. _analyze_complete_impair_pair_index (ligne 5189)
18. _analyze_complete_desync_sync_index (ligne 5259)
19. _analyze_complete_combined_index (ligne 5309)
20. _analyze_complete_pbt_index (ligne 5346)
21. _analyze_complete_so_index (ligne 5394)

SYNTHÈSE ET IMPACTS CROISÉS :
22. _synthesize_complete_analysis (ligne 5450)
23. _analyze_complete_cross_impacts (ligne 5507)
24. _generate_complete_synthesis (ligne 2877)
25. _calculate_cross_index_impacts (ligne 2967)
26. _calculate_variations_impact (ligne 3067)
27. _calculate_global_strength_metrics (ligne 3121)

ANALYSES D'IMPACTS SPÉCIALISÉES (20 méthodes) :
28. _analyze_impair_pair_to_so_impact (ligne 5592)
29. _analyze_desync_sync_to_pbt_impact (ligne 5620)
30. _analyze_desync_sync_to_so_impact (ligne 5678)
31. _analyze_combined_to_pbt_impact (ligne 5706)
32. _analyze_combined_to_so_impact (ligne 5750)
33. _analyze_tri_dimensional_impacts (ligne 5786)
34. _analyze_variations_impact_on_outcomes (ligne 5828)
35. _analyze_consecutive_length_impact (ligne 5893)
36. _analyze_transition_moments_impact (ligne 6467)
37. _analyze_desync_periods_impact (ligne 6684)
38. _analyze_combined_state_changes_impact (ligne 6940)
39. _analyze_temporal_correlation_evolution (ligne 7223)

MÉTHODES UTILITAIRES ET CALCULS (8 méthodes) :
40-47. Méthodes de calcul, distribution, corrélations, etc.

================================================================================
🎯 ROLLOUT 2 - GÉNÉRATEUR (CLUSTER PAR DÉFAUT)
================================================================================

MÉTHODE PRINCIPALE :
48. _rollout_generator (ligne 3201)
    📝 Rollout 2 Générateur - Génération séquences candidates
    🎯 Version cluster par défaut (0-1)
    ⚠️ REMPLACÉE par version universelle dans azr_baccarat_predictor.py

MÉTHODES SUPPORT ROLLOUT 2 (35 méthodes) :

GÉNÉRATION OPTIMISÉE :
49. _define_optimized_generation_space (ligne 4531)
50. _generate_sequences_from_signals (ligne 4568)
51. _generate_fallback_sequences (ligne 4654)
52. _enrich_sequences_with_complete_indexes (ligne 10034)

GÉNÉRATION SPÉCIALISÉE (15 méthodes) :
53. _generate_impair_pair_optimized_sequence (ligne 9167)
54. _generate_sync_based_sequence (ligne 9346)
55. _generate_combined_index_sequence (ligne 9550)
56. _generate_so_pattern_sequence (ligne 9787)
57-67. Autres méthodes de génération spécialisée

MÉTHODES UTILITAIRES GÉNÉRATION (15 méthodes) :
68-82. Méthodes de calcul, conversion, classification, etc.

================================================================================
🎯 ROLLOUT 3 - PRÉDICTEUR (CLUSTER PAR DÉFAUT)
================================================================================

MÉTHODE PRINCIPALE :
83. _rollout_predictor (ligne 3308)
    📝 Rollout 3 Prédicteur - Sélection séquence optimale finale
    🎯 Version cluster par défaut (0-1)
    ⚠️ REMPLACÉE par version universelle dans azr_baccarat_predictor.py

MÉTHODES SUPPORT ROLLOUT 3 (15 méthodes) :

ÉVALUATION QUALITÉ :
84. _evaluate_sequence_quality (ligne 3437, 10849)
85. _evaluate_signal_alignment (ligne 3473)
86. _evaluate_fallback_alignment (ligne 3529)
87. _analyze_sequence_consistency (ligne 3553)

ÉVALUATION RISQUE/RÉCOMPENSE :
88. _assess_risk_reward_ratio (ligne 3613)
89. _validate_sequence_logic (ligne 3659)
90. _calculate_sequence_score (ligne 3720)

SÉLECTION ET CONFIANCE :
91. _select_best_sequence (ligne 3749, 10892)
92. _calculate_cluster_confidence (ligne 3788, 10906)
93. _calculate_cluster_confidence_azr_calibrated (ligne 3833)
94. _calculate_confidence_risk_factors (ligne 3916)
95. _calculate_epistemic_uncertainty (ligne 3975)
96. _calculate_rollout_consensus (ligne 4013)

EXTRACTION ET CONVERSION :
97. _extract_next_hand_prediction (ligne 4062, 10927)
98. _convert_pb_sequence_to_so (ligne 4096)
99. _get_last_historical_pb_result (ligne 4146)

================================================================================
🔧 MÉTHODES SYSTÈME ET UTILITAIRES (54 méthodes)
================================================================================

MÉTHODES SPÉCIALISÉES CLUSTERS :
100. _rollout_analyzer_c2_patterns_courts (ligne 909)
101. _rollout_analyzer_c3_patterns_moyens (ligne 222)
102. _analyze_impair_consecutive_bias_c2_specialized (ligne 1096)
103. _analyze_sync_alternation_bias_c2_specialized (ligne 1207)
104. _analyze_impair_consecutive_bias_c3_specialized (ligne 2043)
105. _analyze_sync_alternation_bias_c3_specialized (ligne 2128)

MÉTHODES GÉNÉRATION SPÉCIALISÉES :
106. _generate_bias_signals_summary_c2 (ligne 1328)
107. _generate_bias_generation_guidance_c2 (ligne 1351)
108. _generate_bias_quick_access_c2 (ligne 1374)

MÉTHODES SYSTÈME :
109-152. Méthodes utilitaires, calculs, spécialisations clusters, etc.

================================================================================
📊 STATUT DES MÉTHODES CLUSTER PAR DÉFAUT
================================================================================

⚠️ TOUTES LES MÉTHODES DANS class.txt SONT OBSOLÈTES
✅ Remplacées par versions universelles dans azr_baccarat_predictor.py
✅ Les rollouts universels s'adaptent automatiquement au cluster par défaut
✅ Parameter Object Pattern implémenté avec AZRConfig

================================================================================
🔍 CONCLUSION
================================================================================

Le fichier class.txt contient 152 méthodes qui représentent l'ANCIENNE
architecture spécialisée par cluster. Ces méthodes ont été remplacées par
133 méthodes universelles dans azr_baccarat_predictor.py qui s'adaptent
automatiquement aux 8 clusters via le Parameter Object Pattern.

L'universalisation est COMPLÈTEMENT TERMINÉE.
