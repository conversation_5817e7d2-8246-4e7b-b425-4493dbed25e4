LISTE DES MÉTHODES À UNIVERSALISER - CLUSTER 0 ROLLOUTS 1, 2, 3
================================================================
Date de création: 07/06/2025 18:30
Basé sur l'analyse corrective complète de class.txt

TOTAL : 24 MÉTHODES DU CLUSTER 0 À UNIVERSALISER
===============================================

ROLLOUT 1 - ANALYSES COMPLÈTES DES 5 INDICES (8 méthodes)
=========================================================

1. _analyze_complete_desync_sync_index (ligne 5259)
   - Analyse complète INDEX 2 : DESYNC/SYNC
   - Calcule la synchronisation entre position attendue et résultat réel

2. _analyze_complete_combined_index (ligne 5309)
   - Analyse complète INDEX 3 : COMBINÉ
   - Combine les indices IMPAIR/PAIR et DESYNC/SYNC

3. _analyze_complete_pbt_index (ligne 5346)
   - Analyse complète INDEX 4 : P/B/T
   - Analyse détaillée des résultats Player/Banker/Tie

4. _analyze_complete_so_index (ligne 5394)
   - Analyse complète INDEX 5 : S/O (SAME/OPPOSITE)
   - Analyse patterns de répétition vs changement sur P/B uniquement

ROLLOUT 1 - SYNTHÈSES COMPLÈTES (4 méthodes)
============================================

5. _synthesize_complete_analysis (ligne 5450)
   - Synthèse finale de l'analyse complète des 5 indices
   - AVEC ANALYSE COMPLÈTE DES IMPACTS CROISÉS

6. _generate_complete_synthesis (ligne 2877)
   - Génère la synthèse complète de l'analyse des 5 indices

7. _calculate_cross_index_impacts (ligne 2967)
   - Calcule les impacts croisés entre les différents indices

8. _analyze_complete_cross_impacts (ligne 5507)
   - Analyse complète des impacts croisés entre tous les indices

ROLLOUT 2 - GÉNÉRATION OPTIMISÉE PAR INDICE (8 méthodes)
========================================================

9. _define_optimized_generation_space (ligne 4531)
   - Définit l'espace de génération optimisé basé sur les analyses

10. _generate_sequences_from_signals (ligne 4568)
    - Génère des séquences candidates à partir des signaux de biais

11. _generate_fallback_sequences (ligne 4654)
    - Génère des séquences de fallback en cas de signaux faibles

12. _generate_impair_pair_optimized_sequence (ligne 9167)
    - Génère séquence optimisée basée sur corrélations IMPAIR/PAIR → P/B

13. _generate_sync_based_sequence (ligne 9346)
    - Génère séquence basée sur les patterns SYNC/DESYNC

14. _generate_combined_index_sequence (ligne 9550)
    - Génère séquence basée sur l'indice combiné

15. _generate_so_pattern_sequence (ligne 9787)
    - Génère séquence basée sur les patterns S/O

16. _enrich_sequences_with_complete_indexes (ligne 10034)
    - Enrichit les séquences avec les analyses complètes des 5 indices

ROLLOUT 3 - PRÉDICTION AVANCÉE (4 méthodes)
===========================================

17. _calculate_cluster_confidence_azr_calibrated (ligne 3833)
    - Calcule la confiance cluster calibrée selon AZR

18. _calculate_confidence_risk_factors (ligne 3916)
    - Calcule les facteurs de risque pour la confiance

19. _calculate_epistemic_uncertainty (ligne 3975)
    - Calcule l'incertitude épistémique

20. _calculate_rollout_consensus (ligne 4013)
    - Calcule le consensus entre les rollouts

MÉTHODES SUPPORT UNIVERSELLES (4 méthodes)
==========================================

21. _extract_next_hand_prediction (ligne 4062)
    - Extrait la prédiction de la prochaine manche

22. _convert_pb_sequence_to_so (ligne 4096)
    - Convertit une séquence P/B en séquence S/O

23. _get_last_historical_pb_result (ligne 4146)
    - Récupère le dernier résultat P/B historique

24. _calculate_variations_impact (ligne 3067)
    - Calcule l'impact des variations sur les prédictions

RÉPARTITION PAR CATÉGORIE
=========================
✅ Rollout 1 - Analyses complètes : 8 méthodes (33%)
✅ Rollout 2 - Génération optimisée : 8 méthodes (33%)
✅ Rollout 3 - Prédiction avancée : 4 méthodes (17%)
✅ Support universel : 4 méthodes (17%)

STATUT ACTUEL
=============
❌ Toutes ces 24 méthodes sont ABSENTES de AZRCluster
❌ Elles doivent être copiées depuis class.txt
❌ Puis universalisées selon la méthodologie établie

OBJECTIF FINAL
==============
✅ Universaliser ces 24 méthodes du cluster 0
✅ Les intégrer dans AZRCluster avec paramètres centralisés
✅ Permettre 8 comportements différents (clusters 0-7)
✅ Éliminer toute duplication de code

MÉTHODOLOGIE D'UNIVERSALISATION
===============================
1. Copie identique depuis class.txt vers AZRCluster
2. Audit des spécialisations conditionnelles
3. Centralisation paramètres dans AZRConfig
4. Suppression conditions if self.cluster_id ==
5. Validation universalité pour tous clusters

RÉFÉRENCE
=========
Basé sur l'analyse corrective de class.txt (10,953 lignes)
Méthodes identifiées sans suffixe cluster spécialisé
Appartenant exclusivement au cluster 0 (par défaut)
Supportant les rollouts 1, 2 et 3 du cluster 0
