MÉTHODE : _extract_temporal_evolution_strength
LIGNE DÉBUT : 8093
SIGNATURE : def _extract_temporal_evolution_strength(self, temporal_impacts: Dict) -> float:
================================================================================

    def _extract_temporal_evolution_strength(self, temporal_impacts: Dict) -> float:
        """Extrait la force de l'évolution temporelle des corrélations (focus P/B et S/O)"""

        if not temporal_impacts:
            return 0.0

        # Chercher métriques dans temporal_strength_metrics
        if 'temporal_strength_metrics' in temporal_impacts:
            metrics = temporal_impacts['temporal_strength_metrics']
            evolution_strength = metrics.get('evolution_strength', 0.0)
            temporal_consistency = metrics.get('temporal_consistency', 0.0)
            temporal_predictability = metrics.get('temporal_predictability', 0.0)

            # <PERSON><PERSON><PERSON> pondérée (évolution plus importante)
            return (evolution_strength * 2 + temporal_consistency + temporal_predictability) / 4

        return 0.0

