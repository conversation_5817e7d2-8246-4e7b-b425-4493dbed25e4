MÉTHODE : is_game_complete
LIGNE DÉBUT : 10358
SIGNATURE : def is_game_complete(self, pb_hands: int, so_conversions: int, mode: str = "real") -> bool:
================================================================================

    def is_game_complete(self, pb_hands: int, so_conversions: int, mode: str = "real") -> bool:
        """
        Vérifie si la partie est terminée selon les limites

        Args:
            pb_hands: Nombre de manches P/B jouées
            so_conversions: Nombre de conversions S/O
            mode: Mode de jeu ("real" ou "training")

        Returns:
            True si la partie doit se terminer
        """
        max_pb = self.get_max_sequence_length(mode)
        max_so = self.get_max_so_conversions(mode)

        return pb_hands >= max_pb or so_conversions >= max_so

    # ========================================================================
    # MÉTHODES DE GÉNÉRATION DES SECTIONS OPTIMISÉES POUR ROLLOUT 2
    # ========================================================================

