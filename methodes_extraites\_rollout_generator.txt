MÉTHODE : _rollout_generator
LIGNE DÉBUT : 3201
SIGNATURE : def _rollout_generator(self, analyzer_report: Dict) -> Dict:
================================================================================

    def _rollout_generator(self, analyzer_report: Dict) -> Dict:
        """
        Rollout 2 Générateur - Génération séquences candidates basées sur analyse complète

        Exploite l'analyse complète des 5 indices pour génération optimale.
        Génère 4 séquences candidates avec stratégies distinctes.

        Args:
            analyzer_report: Rapport complet du rollout analyseur avec 5 indices

        Returns:
            Dict: Résultat structuré contenant séquences candidates et métadonnées
                - sequences: List[Dict] - Séquences candidates enrichies
                - generation_metadata: Dict - Métadonnées de génération
                - signals_used: Dict - Signaux utilisés pour la génération
                - generation_stats: Dict - Statistiques de génération
        """
        try:
            if 'error' in analyzer_report:
                return {
                    'sequences': [],
                    'generation_metadata': {
                        'total_sequences_generated': 0,
                        'generation_strategy': 'analyzer_error',
                        'cluster_id': self.cluster_id,
                        'generation_timestamp': time.time()
                    },
                    'error': 'Analyzer report contains error',
                    'analyzer_error': analyzer_report.get('error')
                }

            # ================================================================
            # NOUVEAU : UTILISATION DES SECTIONS OPTIMISÉES DU ROLLOUT 1
            # ================================================================

            # Extraction des sections optimisées pour le générateur
            signals_summary = analyzer_report.get('signals_summary', {})
            generation_guidance = analyzer_report.get('generation_guidance', {})
            quick_access = analyzer_report.get('quick_access', {})

            # Extraction analyse complète des 5 indices (fallback)
            indices_analysis = analyzer_report.get('indices_analysis', {})
            synthesis = analyzer_report.get('synthesis', {})
            sequence_metadata = analyzer_report.get('sequence_metadata', {})

            # Définition espace de génération basé sur les nouvelles sections optimisées
            generation_space = self._define_optimized_generation_space(
                signals_summary, generation_guidance, quick_access,
                indices_analysis, synthesis, sequence_metadata
            )

            # ================================================================
            # GÉNÉRATION OPTIMISÉE BASÉE SUR LES SIGNAUX DU ROLLOUT 1
            # ================================================================

            # Génération de séquences candidates basées sur les signaux optimisés
            candidates = self._generate_sequences_from_signals(
                signals_summary, generation_guidance, quick_access, generation_space
            )

            # Fallback : Génération classique si pas de signaux optimisés
            if not candidates:
                candidates = self._generate_fallback_sequences(generation_space)

            # Enrichissement séquences avec tous les indices
            enriched_candidates = self._enrich_sequences_with_complete_indexes(candidates, indices_analysis, synthesis)

            # NOUVEAU : Retourner un dictionnaire structuré au lieu d'une liste
            generator_result = {
                'sequences': enriched_candidates,
                'generation_metadata': {
                    'total_sequences_generated': len(enriched_candidates),
                    'generation_strategy': 'signals_based',
                    'cluster_id': self.cluster_id,
                    'generation_timestamp': time.time()
                },
                'signals_used': {
                    'signals_summary': signals_summary,
                    'generation_guidance': generation_guidance,
                    'quick_access': quick_access
                },
                'generation_stats': {
                    'fallback_used': len(candidates) == 0,
                    'enrichment_applied': True,
                    'avg_sequence_length': sum(len(seq) for seq in enriched_candidates) / len(enriched_candidates) if enriched_candidates else 0
                }
            }

            return generator_result

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Erreur rollout generator cluster {self.cluster_id}: {type(e).__name__}: {e}")
            logger.error(f"Détails erreur cluster {self.cluster_id}: {error_details}")
            return {
                'sequences': [],
                'generation_metadata': {
                    'total_sequences_generated': 0,
                    'generation_strategy': 'error_fallback',
                    'cluster_id': self.cluster_id,
                    'generation_timestamp': time.time()
                },
                'error': str(e),
                'error_details': error_details
            }

