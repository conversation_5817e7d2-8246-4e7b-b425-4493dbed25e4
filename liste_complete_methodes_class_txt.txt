LISTE COMPLÈTE DES 162 MÉTHODES DE class.txt
==============================================
Date de création: 07/06/2025
Source: c:\Users\<USER>\Desktop\base\centralisation_methodes\class.txt
Total: 162 méthodes identifiées

MÉTHODES EXTRAITES PAR ANALYSE REGEX :
=====================================

1. __init__(self, cluster_id: int, config: AZRConfig, predictor_instance=None)
2. execute_cluster_pipeline(self, standardized_sequence: Dict) -> Dict
3. _rollout_analyzer(self, standardized_sequence: Dict) -> Dict
4. _rollout_analyzer_c3_patterns_moyens(self, standardized_sequence: Dict) -> Dict
5. _analyze_impair_consecutive_bias(self, hands_data: List) -> Dict
6. _analyze_pair_priority_2_autonomous(self, hands_data: List, impair_bias_analysis: Dict) -> Dict
7. _analyze_sync_alternation_bias(self, hands_data: List) -> Dict
8. _rollout_analyzer_c2_patterns_courts(self, standardized_sequence: Dict) -> Dict
9. _analyze_impair_consecutive_bias_c2_specialized(self, hands_data: List, position_types: List, ...)
10. _analyze_sync_alternation_bias_c2_specialized(self, hands_data: List) -> Dict
11. _apply_c2_short_patterns_specialization(self, base_analysis: Dict) -> Dict
12. _generate_bias_signals_summary_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict
13. _generate_bias_generation_guidance_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict
14. _generate_bias_quick_access_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict
15. _get_cluster_specialization_params(self, cluster_id: int) -> Dict
16. _create_generic_cluster_analyzer(self, cluster_id: int, standardized_sequence: Dict) -> Dict
17. _analyze_impair_bias_specialized(self, hands_data: List, position_types: List, ...)
18. _analyze_sync_bias_specialized(self, hands_data: List, cluster_id: int, spec_params: Dict) -> Dict
19. _apply_cluster_specialization(self, base_analysis: Dict, cluster_id: int, spec_params: Dict) -> Dict
20. _correlate_impair_with_sync(self, isolated_impairs: List, consecutive_sequences: List, sync_states: List) -> Dict
21. _correlate_impair_with_combined(self, isolated_impairs: List, consecutive_sequences: List, combined_states: List) -> Dict
22. _correlate_impair_with_pb(self, isolated_impairs: List, consecutive_sequences: List, pb_outcomes: List, total_hands: int) -> Dict
23. _correlate_impair_with_so(self, isolated_impairs: List, consecutive_sequences: List, so_outcomes: List, total_hands: int) -> Dict
24. _correlate_bias_to_pb_variations(self, impair_bias: Dict, sync_bias: Dict, combined_bias: Dict, hands_data: List) -> Dict
25. _correlate_bias_to_so_variations(self, pb_correlation: Dict, hands_data: List) -> Dict
26. _analyze_impair_consecutive_bias_c3_specialized(self, hands_data: List, position_types: List, ...)
27. _analyze_sync_alternation_bias_c3_specialized(self, hands_data: List) -> Dict
28. _apply_c3_medium_patterns_specialization(self, base_analysis: Dict) -> Dict
29. _analyze_combined_structural_bias(self, impair_bias: Dict, sync_bias: Dict, hands_data: List) -> Dict
30. _correlate_bias_to_pb_variations(self, impair_bias: Dict, sync_bias: Dict, combined_bias: Dict, hands_data: List) -> Dict
31. _correlate_bias_to_so_variations(self, pb_correlation: Dict, hands_data: List) -> Dict
32. _generate_priority_based_synthesis_autonomous(self, bias_analyses: Dict, hands_data: List) -> Dict
33. _generate_bias_exploitation_synthesis(self, bias_analyses: Dict, hands_data: List) -> Dict
34. _generate_bias_signals_summary(self, bias_synthesis: Dict) -> Dict
35. _generate_bias_generation_guidance(self, bias_synthesis: Dict) -> Dict
36. _generate_bias_quick_access(self, bias_synthesis: Dict) -> Dict
37. _generate_complete_synthesis(self, all_indices: Dict, hands_data: List) -> Dict
38. _calculate_cross_index_impacts(self, all_indices: Dict) -> Dict
39. _calculate_variations_impact(self, all_indices: Dict) -> Dict
40. _calculate_global_strength_metrics(self, all_indices: Dict) -> Dict
41. _rollout_generator(self, analyzer_report: Dict) -> Dict
42. _rollout_predictor(self, generator_result: Dict, analyzer_report: Dict) -> Dict
43. _evaluate_sequence_quality(self, sequence: Dict, analyzer_report: Dict) -> Dict
44. _evaluate_signal_alignment(self, sequence: Dict, analyzer_report: Dict) -> float
45. _evaluate_fallback_alignment(self, sequence: Dict, analyzer_report: Dict) -> float
46. _analyze_sequence_consistency(self, sequence: Dict) -> float
47. _assess_risk_reward_ratio(self, sequence: Dict, analyzer_report: Dict) -> float
48. _validate_sequence_logic(self, sequence: Dict, analyzer_report: Dict) -> float
49. _calculate_sequence_score(self, evaluation: Dict) -> float
50. _select_best_sequence(self, evaluated_sequences: List[Dict]) -> Dict
51. _calculate_cluster_confidence(self, best_sequence: Dict, analyzer_report: Dict) -> float
52. _calculate_cluster_confidence_azr_calibrated(self, best_sequence: Dict, analyzer_report: Dict) -> float
53. _calculate_confidence_risk_factors(self, best_sequence: Dict, analyzer_report: Dict) -> float
54. _calculate_epistemic_uncertainty(self, analyzer_report: Dict) -> float
55. _calculate_rollout_consensus(self, best_sequence: Dict, analyzer_report: Dict) -> float
56. _extract_next_hand_prediction(self, best_sequence: Dict) -> str
57. _convert_pb_sequence_to_so(self, pb_sequence: List[str], analyzer_report: Dict) -> List[str]
58. _get_last_historical_pb_result(self, analyzer_report: Dict) -> str
59. calculate_rollout2_reward(self, sequence_quality: float, diversity_score: float, difficulty_factor: float) -> Dict
60. calculate_rollout2_sequence_quality(self, sequences: List[Dict]) -> float
61. calculate_rollout2_diversity_score(self, sequences: List[Dict]) -> float
62. calculate_rollout3_reward(self, prediction: str, actual_outcome: str, confidence: float, risk_factor: float) -> Dict
63. calculate_rollout3_risk_factor(self, prediction_data: Dict, analyzer_report: Dict) -> float
64. calculate_cluster_total_reward(self, rollout1_result: Dict, rollout2_result: Dict, rollout3_result: Dict, actual_outcome: str = None) -> Dict
65. _define_optimized_generation_space(self, signals_summary: Dict, generation_guidance: Dict, ...)
66. _generate_sequences_from_signals(self, signals_summary: Dict, generation_guidance: Dict, ...)
67. _generate_sequence_from_signal(self, signal: Dict, generation_space: Dict) -> List[str]
68. _generate_fallback_sequences(self, generation_space: Dict) -> List[Dict]
69. _classify_confidence_level(self, signal_confidence: float) -> str
70. _generate_so_based_sequence(self, target_outcome: str, sequence_length: int, generation_space: Dict) -> List[str]
71. _generate_all_possible_sequences(self, generation_space: Dict) -> List[Dict]
72. _convert_pb_sequence_to_so_with_history(self, pb_sequence: List[str], last_historical_pb: str) -> List[str]
73. _calculate_sequence_probability(self, sequence: List[str], generation_space: Dict) -> float
74. _calculate_sequence_quality_metrics(self, sequence: List[str], generation_space: Dict) -> Dict
75. _generate_pb_sequence(self, target_pb: str, sequence_length: int, generation_space: Dict) -> List[str]
76. _generate_pair_sync_sequence(self, sequence_length: int, generation_space: Dict) -> List[str]
77. _generate_impair_sync_sequence(self, sequence_length: int, generation_space: Dict) -> List[str]
78. _generate_generic_signal_sequence(self, signal: Dict, sequence_length: int, generation_space: Dict) -> List[str]
79. _analyze_complete_impair_pair_index(self, hands_data: List) -> Dict
80. _analyze_complete_desync_sync_index(self, hands_data: List) -> Dict
81. _analyze_complete_combined_index(self, impair_pair_data: Dict, desync_sync_data: Dict, hands_data: List) -> Dict
82. _analyze_complete_pbt_index(self, hands_data: List) -> Dict
83. _analyze_complete_so_index(self, hands_data: List) -> Dict
84. _synthesize_complete_analysis(self, all_indices: Dict) -> Dict
85. _analyze_complete_cross_impacts(self, all_indices: Dict) -> Dict
86. _analyze_impair_pair_to_so_impact(self, impair_pair_seq: List[str], so_seq: List[str]) -> Dict
87. _analyze_desync_sync_to_pbt_impact(self, desync_sync_seq: List[str], pbt_seq: List[str]) -> Dict
88. _identify_desync_periods(self, sync_sequence: List[str]) -> List[Dict]
89. _analyze_desync_sync_to_so_impact(self, desync_sync_seq: List[str], so_seq: List[str]) -> Dict
90. _analyze_combined_to_pbt_impact(self, combined_seq: List[str], pbt_seq: List[str]) -> Dict
91. _analyze_combined_to_so_impact(self, combined_seq: List[str], so_seq: List[str]) -> Dict
92. _analyze_tri_dimensional_impacts(self, impair_pair_seq: List[str], desync_sync_seq: List[str], ...)
93. _analyze_variations_impact_on_outcomes(self, all_indices: Dict) -> Dict
94. _analyze_consecutive_length_impact(self, impair_pair_seq: List[str], pbt_seq: List[str], so_seq: List[str]) -> Dict
95. _find_consecutive_sequences_with_positions(self, sequence: List[str], pattern: str) -> List[Dict]
96. _find_consecutive_sequences(self, sequence: List[str], pattern: str) -> List[int]
97. _calculate_asymmetric_impair_alert_level(self, impair_consecutive: int) -> int
98. _calculate_asymmetric_pair_alert_level(self, pair_consecutive: int) -> int
99. _calculate_impair_rarity_score(self, impair_consecutive: int) -> float
100. _calculate_pair_commonality_score(self, pair_consecutive: int) -> float
101. _calculate_asymmetric_significance(self, impair_consecutive: int, pair_consecutive: int) -> Dict
102. _identify_dominant_desync_sync_so_pattern(self, sync_s_count: int, sync_o_count: int, ...)
103. _calculate_combined_so_impact_strength(self, impact_analysis: Dict) -> float
104. _calculate_combined_pbt_impact_strength(self, impact_analysis: Dict) -> float
105. _identify_dominant_impair_pair_so_pattern(self, impair_s_count: int, impair_o_count: int, ...)
106. _calculate_overall_impact_strength(self, cross_impacts: Dict) -> float
107. _analyze_transition_moments_impact(self, impair_pair_seq: List[str], desync_sync_seq: List[str], ...)
108. _calculate_distribution(self, sequence: List[str], possible_values: List[str]) -> Dict
109. _analyze_desync_periods_impact(self, desync_periods: List[Dict], pbt_seq: List[str], so_seq: List[str]) -> Dict
110. _analyze_combined_state_changes_impact(self, combined_seq: List[str], pbt_seq: List[str], so_seq: List[str]) -> Dict
111. _analyze_temporal_correlation_evolution(self, impair_pair_seq: List[str], desync_sync_seq: List[str], ...)
112. _calculate_phase_impair_pair_pb_correlation(self, impair_pair_seq: List[str], pbt_seq: List[str]) -> Dict
113. _calculate_phase_impair_pair_so_correlation(self, impair_pair_seq: List[str], so_seq: List[str]) -> Dict
114. _calculate_phase_sync_desync_pb_correlation(self, desync_sync_seq: List[str], pbt_seq: List[str]) -> Dict
115. _calculate_phase_sync_desync_so_correlation(self, desync_sync_seq: List[str], so_seq: List[str]) -> Dict
116. _calculate_phase_correlation_strength(self, impair_pb_corr: Dict, impair_so_corr: Dict, ...)
117. _analyze_correlation_trend(self, correlation_values: List[float], trend_name: str) -> Dict
118. _calculate_correlation_stability(self, phase_strengths: Dict) -> float
119. _calculate_variance(self, values: List[float]) -> float
120. _generate_temporal_recommendation(self, best_phase: str, stability: float, trends: Dict) -> str
121. _calculate_evolution_strength(self, trends: Dict) -> float
122. _calculate_temporal_consistency(self, phase_strengths: Dict) -> float
123. _calculate_temporal_predictability(self, trends: Dict) -> float
124. _calculate_variation_strength_analysis(self, variations_impact: Dict) -> Dict
125. _extract_consecutive_length_strength(self, consecutive_impacts: Dict) -> float
126. _extract_transition_moments_strength(self, transition_impacts: Dict) -> float
127. _extract_desync_periods_strength(self, desync_impacts: Dict) -> float
128. _extract_combined_state_changes_strength(self, combined_impacts: Dict) -> float
129. _extract_temporal_evolution_strength(self, temporal_impacts: Dict) -> float
130. _calculate_confidence_level(self, global_strength: float, valid_strengths: int) -> str
131. _generate_exploitation_recommendation(self, global_strength: float, dominant_type: str, ...)
132. _identify_best_prediction_context(self, variations_impact: Dict, dominant_type: str, ...)
133. _calculate_strength_distribution(self, individual_strengths: Dict, weights: Dict) -> Dict
134. _calculate_variation_consistency(self, individual_strengths: Dict) -> float
135. _assess_sample_size_adequacy(self, variations_impact: Dict) -> float
136. _calculate_statistical_significance(self, individual_strengths: Dict, variations_impact: Dict) -> float
137. _calculate_pattern_stability(self, individual_strengths: Dict) -> float
138. _assess_overall_quality(self, consistency: float, sample_adequacy: float, ...)
139. _identify_enhanced_dominant_correlations(self, all_indices: Dict, cross_index_impacts: Dict) -> List[Dict]
140. _identify_enhanced_high_confidence_zones(self, all_indices: Dict, cross_index_impacts: Dict) -> List[Dict]
141. _define_complete_generation_space_DEPRECATED(self, indices_analysis: Dict, synthesis: Dict, sequence_metadata: Dict) -> Dict
142. _generate_impair_pair_optimized_sequence(self, generation_space: Dict) -> List[Dict]
143. _generate_sync_based_sequence(self, generation_space: Dict) -> List[Dict]
144. _generate_combined_index_sequence(self, generation_space: Dict) -> List[Dict]
145. _generate_so_pattern_sequence(self, generation_space: Dict) -> List[Dict]
146. _enrich_sequences_with_complete_indexes(self, sequences: List[List[Dict]], indices_analysis: Dict, synthesis: Dict) -> List[List[Dict]]
147. _classify_combined_transition_type(self, from_state: str, to_state: str) -> str
148. get_max_sequence_length(self, mode: str = "real") -> int
149. get_max_so_conversions(self, mode: str = "real") -> int
150. is_game_complete(self, pb_hands: int, so_conversions: int, mode: str = "real") -> bool
151. _generate_signals_summary(self, all_indices: Dict, synthesis: Dict) -> Dict
152. _generate_generation_guidance(self, all_indices: Dict, synthesis: Dict, signals_summary: Dict) -> Dict
153. _generate_quick_access(self, all_indices: Dict, synthesis: Dict, signals_summary: Dict) -> Dict
154. _update_performance_metrics(self, prediction: Dict, total_time: float)
155. _count_consecutive_pattern(self, sequence: List[str], pattern: str) -> int
156. _calculate_rupture_probability(self, impair_count: int, pair_count: int) -> float
157. _analyze_correlations_std_dev(self, pair_impair_seq: List[str], ...)
158. _identify_improbability_zones(self, impair_count: int, pair_count: int, ...)
159. _evaluate_sequence_quality(self, sequence, analyzer_report: Dict) -> Dict
160. _select_best_sequence(self, evaluated_sequences: List[Dict]) -> Dict
161. _calculate_cluster_confidence(self, best_sequence: Dict, analyzer_report: Dict) -> float
162. _extract_next_hand_prediction(self, best_sequence) -> Dict
