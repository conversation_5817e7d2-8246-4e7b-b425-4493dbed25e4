MÉTHODE : _extract_next_hand_prediction
LIGNE DÉBUT : 10927
SIGNATURE : def _extract_next_hand_prediction(self, best_sequence) -> Dict:
================================================================================

    def _extract_next_hand_prediction(self, best_sequence) -> Dict:
        """Extraction prédiction prochaine manche"""
        # Adaptation à la structure des données (liste ou dictionnaire)
        if isinstance(best_sequence, dict):
            sequence_data = best_sequence.get('sequence_data', [])
        else:
            sequence_data = best_sequence  # C'est déjà une liste

        if sequence_data and len(sequence_data) > 0:
            next_hand = sequence_data[0]  # Premier élément de la séquence
            if isinstance(next_hand, dict):
                return {
                    'predicted_so': next_hand.get('so_conversion', 'S'),
                    'predicted_parity': next_hand.get('parity', 'PAIR'),
                    'predicted_sync': next_hand.get('sync_state', 'SYNC'),
                    'predicted_combined': next_hand.get('combined_state', 'PAIR_SYNC'),
                    'confidence': next_hand.get('generation_confidence', 0.7)
                }

        return {
            'predicted_so': 'S',
            'predicted_parity': 'PAIR',
            'predicted_sync': 'SYNC',
            'predicted_combined': 'PAIR_SYNC',
            'confidence': 0.5
        }
