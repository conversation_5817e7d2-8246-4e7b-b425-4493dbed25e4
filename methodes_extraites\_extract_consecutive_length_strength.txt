MÉTHODE : _extract_consecutive_length_strength
LIGNE DÉBUT : 7974
SIGNATURE : def _extract_consecutive_length_strength(self, consecutive_impacts: Dict) -> float:
================================================================================

    def _extract_consecutive_length_strength(self, consecutive_impacts: Dict) -> float:
        """Extrait la force des impacts de longueurs consécutives (focus P/B et S/O)"""

        if not consecutive_impacts:
            return 0.0

        total_strength = 0.0
        valid_impacts = 0

        # Analyser impacts IMPAIR sur P/B (sans TIE) et S/O
        for impact_type in ['impair_length_to_pbt', 'impair_length_to_so', 'pair_length_to_pbt', 'pair_length_to_so']:
            impacts = consecutive_impacts.get(impact_type, {})

            for length_key, impact_data in impacts.items():
                if impact_type.endswith('_to_pbt'):
                    # P/B seulement (exclure TIE)
                    player_ratio = impact_data.get('player_ratio', 0)
                    banker_ratio = impact_data.get('banker_ratio', 0)
                    pb_total = player_ratio + banker_ratio

                    if pb_total > 0:
                        # Force = écart par rapport à 50/50 P/B
                        pb_strength = abs((player_ratio / pb_total) - 0.5) if pb_total > 0 else 0
                        sample_weight = min(impact_data.get('sample_size', 0) / 10.0, 1.0)
                        total_strength += pb_strength * sample_weight
                        valid_impacts += 1

                elif impact_type.endswith('_to_so'):
                    # S/O seulement
                    same_ratio = impact_data.get('same_ratio', 0)
                    so_strength = abs(same_ratio - 0.5)
                    sample_weight = min(impact_data.get('sample_size', 0) / 10.0, 1.0)
                    total_strength += so_strength * sample_weight
                    valid_impacts += 1

        return total_strength / valid_impacts if valid_impacts > 0 else 0.0

