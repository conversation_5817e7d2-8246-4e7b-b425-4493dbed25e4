MÉTHODE : _generate_bias_quick_access
LIGNE DÉBUT : 2864
SIGNATURE : def _generate_bias_quick_access(self, bias_synthesis: Dict) -> Dict:
================================================================================

    def _generate_bias_quick_access(self, bias_synthesis: Dict) -> Dict:
        """Génère l'accès rapide aux prédictions basées sur les biais"""
        pb_signal = bias_synthesis.get('exploitation_signals', {}).get('pb_signal', {})
        so_signal = bias_synthesis.get('exploitation_signals', {}).get('so_signal', {})

        return {
            'next_prediction_pb': 'P' if pb_signal.get('signal_strength', self.config.zero_value) > self.config.confidence_medium_threshold else 'B',
            'next_prediction_so': 'S' if so_signal.get('signal_strength', self.config.zero_value) > self.config.confidence_medium_threshold else 'O',
            'prediction_confidence': bias_synthesis.get('exploitation_confidence', 0.0),
            'exploitation_ready': bias_synthesis.get('strongest_bias', {}).get('exploitation_ready', False),
            'bias_type': bias_synthesis.get('strongest_bias', {}).get('bias_type', 'none')
        }

