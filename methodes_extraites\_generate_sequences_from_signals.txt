MÉTHODE : _generate_sequences_from_signals
LIGNE DÉBUT : 4568
SIGNATURE : def _generate_sequences_from_signals(self, signals_summary: Dict, generation_guidance: Dict,
================================================================================

    def _generate_sequences_from_signals(self, signals_summary: Dict, generation_guidance: Dict,
                                       quick_access: Dict, generation_space: Dict) -> List[Dict]:
        """
        Génère des séquences candidates basées sur les signaux optimisés du Rollout 1

        NOUVEAU : Exploitation directe des signaux pour génération ciblée
        """
        candidates = []
        top_signals = signals_summary.get('top_signals', [])

        if not top_signals:
            return []  # Pas de signaux, utiliser fallback

        # NOUVELLE APPROCHE : Génération exhaustive des 8 meilleures séquences
        # Au lieu de se baser uniquement sur les signaux, on génère toutes les possibilités
        # et on sélectionne les 8 meilleures selon leur probabilité

        # Essayer d'abord la génération exhaustive optimisée
        try:
            exhaustive_sequences = self._generate_all_possible_sequences(generation_space)
            if len(exhaustive_sequences) == self.config.rollout2_sequences_count:
                return exhaustive_sequences
        except Exception as e:
            # Fallback vers l'ancienne méthode si problème
            pass

        # Fallback : Génération basée sur les signaux (exactement 8 séquences)
        for i, signal in enumerate(top_signals[:self.config.rollout2_sequences_count]):
            sequence_data = self._generate_sequence_from_signal(signal, generation_space)

            # Probabilité basée sur la confiance du signal
            signal_confidence = signal.get('confidence', self.config.rollout2_signal_confidence_default)
            if signal_confidence > self.config.rollout2_signal_confidence_high:
                estimated_probability = self.config.rollout2_max_probability
            elif signal_confidence > self.config.rollout2_signal_confidence_medium:
                estimated_probability = self.config.rollout2_alternative_probability
            elif signal_confidence > self.config.rollout2_signal_confidence_low:
                estimated_probability = self.config.rollout2_rupture_probability
            else:
                estimated_probability = self.config.rollout2_conservative_probability

            candidates.append({
                'sequence_data': sequence_data,
                'estimated_probability': estimated_probability,
                'strategy': signal.get('strategy', f'{self.config.rollout2_default_strategy_prefix}_{i}'),
                'justification': f"Signal {signal.get('signal_name', self.config.rollout2_default_signal_type)} - {signal.get('strategy', self.config.rollout2_default_justification)}",
                'signal_source': signal,
                'confidence_level': self._classify_confidence_level(signal_confidence)
            })

        return candidates

