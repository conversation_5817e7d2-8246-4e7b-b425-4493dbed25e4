MÉTHODE : _generate_quick_access
LIGNE DÉBUT : 10601
SIGNATURE : def _generate_quick_access(self, all_indices: Dict, synthesis: Dict, signals_summary: Dict) -> Dict:
================================================================================

    def _generate_quick_access(self, all_indices: Dict, synthesis: Dict, signals_summary: Dict) -> Dict:
        """
        Génère l'accès rapide aux données essentielles pour le Rollout 2

        FOCUS : Données critiques en accès immédiat
        """
        quick_access = {
            'current_state': 'unknown',
            'next_prediction_pb': None,
            'next_prediction_so': None,
            'prediction_confidence': 0.0,
            'alert_level': 'LOW',
            'exploitation_ready': False,
            'last_hand_analysis': {},
            'immediate_signals': {}
        }

        # Détermination de l'état actuel
        combined_seq = all_indices.get('combined', {}).get('combined_sequence', [])
        if combined_seq:
            quick_access['current_state'] = combined_seq[-1]  # Dernier état combiné

        # Prédictions basées sur le meilleur signal
        top_signals = signals_summary.get('top_signals', [])
        if top_signals:
            best_signal = top_signals[0]
            quick_access['prediction_confidence'] = best_signal.get('confidence', 0.0)

            # Prédiction P/B
            if best_signal['signal_type'] == 'pb_prediction':
                if 'PLAYER' in best_signal['signal_name']:
                    quick_access['next_prediction_pb'] = 'P'
                elif 'BANKER' in best_signal['signal_name']:
                    quick_access['next_prediction_pb'] = 'B'

            # Prédiction S/O
            if best_signal['signal_type'] == 'so_prediction':
                quick_access['next_prediction_so'] = best_signal.get('target_outcome')

                # Dériver P/B de S/O si possible
                if quick_access['next_prediction_so'] and not quick_access['next_prediction_pb']:
                    pbt_seq = all_indices.get('pbt', {}).get('pbt_sequence', [])
                    if pbt_seq:
                        last_pb = None
                        # Trouver le dernier résultat P/B (ignorer Ties)
                        for result in reversed(pbt_seq):
                            if result in ['P', 'B']:
                                last_pb = result
                                break

                        if last_pb:
                            if quick_access['next_prediction_so'] == 'S':
                                quick_access['next_prediction_pb'] = last_pb  # Same
                            else:  # 'O'
                                quick_access['next_prediction_pb'] = 'B' if last_pb == 'P' else 'P'  # Opposite

        # Niveau d'alerte basé sur la force du signal
        if signals_summary.get('exploitation_ready', False):
            if quick_access['prediction_confidence'] > self.config.critical_threshold:
                quick_access['alert_level'] = 'CRITICAL'  # Signal très fort
            elif quick_access['prediction_confidence'] > self.config.alert_threshold:
                quick_access['alert_level'] = 'HIGH'
            else:
                quick_access['alert_level'] = 'MEDIUM'

        quick_access['exploitation_ready'] = signals_summary.get('exploitation_ready', False)

        # Analyse de la dernière manche
        if all_indices:
            pbt_seq = all_indices.get('pbt', {}).get('pbt_sequence', [])
            impair_pair_seq = all_indices.get('impair_pair', {}).get('position_types', [])
            sync_seq = all_indices.get('desync_sync', {}).get('sync_sequence', [])

            if pbt_seq and impair_pair_seq and sync_seq:
                last_index = len(pbt_seq) - 1
                quick_access['last_hand_analysis'] = {
                    'hand_number': last_index + 1,
                    'result': pbt_seq[last_index],
                    'position_type': impair_pair_seq[last_index] if last_index < len(impair_pair_seq) else 'unknown',
                    'sync_state': sync_seq[last_index] if last_index < len(sync_seq) else 'unknown',
                    'combined_state': combined_seq[last_index] if last_index < len(combined_seq) else 'unknown'
                }

        # Signaux immédiats (top 3 pour accès rapide)
        immediate_signals = {}
        for i, signal in enumerate(top_signals[:3]):
            immediate_signals[f'signal_{i+1}'] = {
                'name': signal['signal_name'],
                'strength': signal['strength'],
                'confidence': signal['confidence'],
                'strategy': signal['strategy']
            }

        quick_access['immediate_signals'] = immediate_signals

        return quick_access

    # ========================================================================
    # MÉTHODES UTILITAIRES CLUSTER AZR
    # ========================================================================

