2025-06-07 23:02:25,856 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-07 23:02:25,857 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-07 23:02:25,857 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-07 23:02:25,857 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-07 23:02:25,858 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-07 23:02:25,858 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-07 23:02:25,858 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-07 23:02:25,863 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-07 23:02:25,863 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-07 23:02:25,864 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-07 23:02:25,864 - INFO - 🧠 <PERSON><PERSON><PERSON>le AZR Baccarat initialisé avec persistance intelligente
2025-06-07 23:02:25,864 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-07 23:02:26,265 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-07 23:02:26,266 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-07 23:02:28,482 - INFO - 🔥 Brûlage initialisé: IMPAIR → État initial: DESYNC
2025-06-07 23:02:28,483 - INFO - 📊 Séquence complète initialisée: ['IMPAIR']
2025-06-07 23:02:28,484 - INFO - 🔥 Cartes brûlées initialisées: IMPAIR → DESYNC
2025-06-07 23:02:31,600 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:02:31,601 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:02:31,675 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,676 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,677 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,677 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,678 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,679 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,680 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,680 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,681 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,681 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,682 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,682 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,683 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_consensus_agreement_threshold'
2025-06-07 23:02:31,684 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 23:02:31,684 - INFO - 📊 Manche #0.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:02:31,685 - INFO - ✅ Manche traitée: P/B#0.0 BANKER PAIR DESYNC -- → Prédiction: O
2025-06-07 23:03:27,407 - INFO - ✅ Validation: Prédit=O, Réel=S, Correct=False, Précision=0.000
2025-06-07 23:03:27,409 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:27,410 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:27,491 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,492 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,492 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,493 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,494 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,495 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,496 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,496 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,497 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,498 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,499 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,499 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,500 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_consensus_agreement_threshold'
2025-06-07 23:03:27,501 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 23:03:27,501 - INFO - 📊 Manche #1.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:03:27,502 - INFO - ✅ Manche traitée: P/B#1.0 BANKER PAIR DESYNC S → Prédiction: O
2025-06-07 23:03:32,273 - INFO - ✅ Validation: Prédit=O, Réel=O, Correct=True, Précision=0.500
2025-06-07 23:03:32,274 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:32,274 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:32,279 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,279 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,280 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,281 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,282 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,282 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,283 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,283 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,284 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,284 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,285 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,287 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,287 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_consensus_agreement_threshold'
2025-06-07 23:03:32,288 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 23:03:32,288 - INFO - 📊 Manche #2.0: PLAYER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:03:32,288 - INFO - ✅ Manche traitée: P/B#2.0 PLAYER PAIR DESYNC O → Prédiction: O
2025-06-07 23:05:47,560 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-07 23:05:47,560 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-07 23:05:47,561 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-07 23:05:47,561 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-07 23:05:47,561 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-07 23:05:47,561 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-07 23:05:47,561 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-07 23:05:47,562 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-07 23:05:47,562 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-07 23:05:47,562 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-07 23:05:47,562 - INFO - 🧠 Modèle AZR Baccarat initialisé avec persistance intelligente
2025-06-07 23:05:47,563 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-07 23:05:48,225 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-07 23:05:48,226 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-07 23:05:51,016 - INFO - 🔥 Brûlage initialisé: IMPAIR → État initial: DESYNC
2025-06-07 23:05:51,016 - INFO - 📊 Séquence complète initialisée: ['IMPAIR']
2025-06-07 23:05:51,017 - INFO - 🔥 Cartes brûlées initialisées: IMPAIR → DESYNC
2025-06-07 23:05:52,468 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'context_bonus_factor'
2025-06-07 23:05:52,468 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'context_bonus_factor'
2025-06-07 23:05:52,547 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,547 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,548 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,549 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,549 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,551 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,552 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,552 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,552 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,553 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,553 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,554 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,555 - ERROR - ❌ Erreur AZR Master: 'AZRConfig' object has no attribute 'cluster_count'
2025-06-07 23:05:52,555 - INFO - 📊 Manche #0.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:05:52,556 - INFO - ✅ Manche traitée: P/B#0.0 BANKER PAIR DESYNC -- → Prédiction: O
2025-06-07 23:08:46,544 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-07 23:08:46,544 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-07 23:08:46,544 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-07 23:08:46,544 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-07 23:08:46,544 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-07 23:08:46,545 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-07 23:08:46,545 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-07 23:08:46,545 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-07 23:08:46,545 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-07 23:08:46,545 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-07 23:08:46,545 - INFO - 🧠 Modèle AZR Baccarat initialisé avec persistance intelligente
2025-06-07 23:08:46,545 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-07 23:08:46,976 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-07 23:08:46,976 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-07 23:08:48,529 - INFO - 🔥 Brûlage initialisé: IMPAIR → État initial: DESYNC
2025-06-07 23:08:48,529 - INFO - 📊 Séquence complète initialisée: ['IMPAIR']
2025-06-07 23:08:48,530 - INFO - 🔥 Cartes brûlées initialisées: IMPAIR → DESYNC
2025-06-07 23:08:49,665 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_1'
2025-06-07 23:08:49,665 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_1'
2025-06-07 23:08:49,741 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,742 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,748 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,748 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,751 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,752 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,753 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,753 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,754 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,754 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,755 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,755 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,756 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 99.6ms)
2025-06-07 23:08:49,757 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-07 23:08:49,757 - INFO - 📊 Manche #0.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: S
2025-06-07 23:08:49,758 - INFO - ✅ Manche traitée: P/B#0.0 BANKER PAIR DESYNC -- → Prédiction: S
