2025-06-07 23:02:25,856 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-07 23:02:25,857 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-07 23:02:25,857 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-07 23:02:25,857 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-07 23:02:25,858 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-07 23:02:25,858 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-07 23:02:25,858 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-07 23:02:25,863 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-07 23:02:25,863 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-07 23:02:25,864 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-07 23:02:25,864 - INFO - 🧠 <PERSON><PERSON><PERSON>le AZR Baccarat initialisé avec persistance intelligente
2025-06-07 23:02:25,864 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-07 23:02:26,265 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-07 23:02:26,266 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-07 23:02:28,482 - INFO - 🔥 Brûlage initialisé: IMPAIR → État initial: DESYNC
2025-06-07 23:02:28,483 - INFO - 📊 Séquence complète initialisée: ['IMPAIR']
2025-06-07 23:02:28,484 - INFO - 🔥 Cartes brûlées initialisées: IMPAIR → DESYNC
2025-06-07 23:02:31,600 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:02:31,601 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:02:31,675 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,676 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,677 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,677 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,678 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,679 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,680 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,680 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,681 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,681 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,682 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,682 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,683 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_consensus_agreement_threshold'
2025-06-07 23:02:31,684 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 23:02:31,684 - INFO - 📊 Manche #0.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:02:31,685 - INFO - ✅ Manche traitée: P/B#0.0 BANKER PAIR DESYNC -- → Prédiction: O
2025-06-07 23:03:27,407 - INFO - ✅ Validation: Prédit=O, Réel=S, Correct=False, Précision=0.000
2025-06-07 23:03:27,409 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:27,410 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:27,491 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,492 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,492 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,493 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,494 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,495 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,496 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,496 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,497 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,498 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,499 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,499 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,500 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_consensus_agreement_threshold'
2025-06-07 23:03:27,501 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 23:03:27,501 - INFO - 📊 Manche #1.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:03:27,502 - INFO - ✅ Manche traitée: P/B#1.0 BANKER PAIR DESYNC S → Prédiction: O
2025-06-07 23:03:32,273 - INFO - ✅ Validation: Prédit=O, Réel=O, Correct=True, Précision=0.500
2025-06-07 23:03:32,274 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:32,274 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:32,279 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,279 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,280 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,281 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,282 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,282 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,283 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,283 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,284 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,284 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,285 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,287 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,287 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_consensus_agreement_threshold'
2025-06-07 23:03:32,288 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 23:03:32,288 - INFO - 📊 Manche #2.0: PLAYER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:03:32,288 - INFO - ✅ Manche traitée: P/B#2.0 PLAYER PAIR DESYNC O → Prédiction: O
