MÉTHODE : calculate_rollout2_diversity_score
LIGNE DÉBUT : 4288
SIGNATURE : def calculate_rollout2_diversity_score(self, sequences: List[Dict]) -> float:
================================================================================

    def calculate_rollout2_diversity_score(self, sequences: List[Dict]) -> float:
        """
        Calcule le score de diversité des séquences générées

        Args:
            sequences: Liste des séquences générées

        Returns:
            float: Score de diversité (0-1)
        """
        if len(sequences) < self.config.two_value:
            return self.config.zero_value

        # Extraire les données de séquences
        sequence_data_list = []
        for sequence in sequences:
            if isinstance(sequence, dict):
                sequence_data = sequence.get('sequence_data', [])
                if sequence_data:
                    # Convertir en chaîne pour comparaison
                    sequence_str = ''.join(str(item) for item in sequence_data)
                    sequence_data_list.append(sequence_str)

        if len(sequence_data_list) < self.config.two_value:
            return self.config.zero_value

        # Calculer la diversité (pourcentage de séquences uniques)
        unique_sequences = len(set(sequence_data_list))
        total_sequences = len(sequence_data_list)

        diversity_score = unique_sequences / total_sequences

        return diversity_score

    # ========================================================================
    # 🏆 SYSTÈME DE RÉCOMPENSES ROLLOUT 3 - FORMULES TRR++
    # ========================================================================

