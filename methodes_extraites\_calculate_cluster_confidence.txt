MÉTHODE : _calculate_cluster_confidence
LIGNE DÉBUT : 10906
SIGNATURE : def _calculate_cluster_confidence(self, best_sequence: Dict, analyzer_report: Dict) -> float:
================================================================================

    def _calculate_cluster_confidence(self, best_sequence: Dict, analyzer_report: Dict) -> float:
        """Calcul confiance finale du cluster"""
        base_confidence = best_sequence.get('estimated_probability', self.config.probability_neutral)
        evaluation_score = best_sequence.get('evaluation', {}).get('total_score', self.config.probability_neutral)

        # Bonus si corrélations fiables détectées
        correlations = analyzer_report.get('correlation_analysis', {})
        correlation_bonus = self.config.zero_value

        for correlation_data in correlations.values():
            if correlation_data.get('std_dev', self.config.one_value) < self.config.correlation_reliability_threshold:
                correlation_bonus += self.config.rollout2_adjustment_small

        # Confiance finale
        final_confidence = min(
            (base_confidence + evaluation_score + correlation_bonus) / self.config.three_value,
            self.config.rollout1_maximum_confidence_value  # Plafonné à 95%
        )

        return final_confidence

