MÉTHODE : _analyze_impair_bias_specialized
LIGNE DÉBUT : 1652
SIGNATURE : def _analyze_impair_bias_specialized(self, hands_data: List, position_types: List,
================================================================================

    def _analyze_impair_bias_specialized(self, hands_data: List, position_types: List,
                                       sync_states: List, combined_states: List,
                                       pb_outcomes: List, so_outcomes: List,
                                       cluster_id: int, spec_params: Dict) -> Dict:
        """
        🎯 ANALYSE IMPAIRS SPÉCIALISÉE GÉNÉRIQUE - Utilise paramètres centralisés

        Applique la spécialisation selon les paramètres centralisés dans AZRConfig.
        """
        # Utiliser la méthode de base
        impair_bias = self._analyze_impair_consecutive_bias(hands_data)

        # Récupérer la fenêtre récente spécialisée du cluster
        cluster_recent_window = self.config.get_cluster_recent_window_size(cluster_id)

        # Appliquer la spécialisation selon les paramètres centralisés
        consecutive_sequences = impair_bias.get('consecutive_impair_sequences', [])
        if consecutive_sequences:
            recent_sequences = consecutive_sequences[-cluster_recent_window:] if len(consecutive_sequences) >= cluster_recent_window else consecutive_sequences

            # Bonus spécialisation selon paramètres centralisés
            specialization_bonus = self.config.zero_value
            for seq in recent_sequences:
                seq_length = len(seq)

                # Appliquer les critères spécifiques au cluster
                if cluster_id in [2, 3, 4]:  # Clusters patterns
                    min_length = spec_params.get('min_length', 2)
                    max_length = spec_params.get('max_length', 10)
                    if min_length <= seq_length <= max_length:
                        specialization_bonus += spec_params.get('bonus', self.config.zero_value) * seq_length
                else:  # Autres clusters
                    specialization_bonus += spec_params.get('bonus', self.config.zero_value)

            impair_bias[f'c{cluster_id}_specialization_bonus'] = min(self.config.one_value, specialization_bonus)
            impair_bias[f'c{cluster_id}_recent_window_applied'] = cluster_recent_window
            impair_bias[f'c{cluster_id}_recent_sequences_count'] = len(recent_sequences)

        # Confiance finale avec bonus spécialisation
        base_confidence = impair_bias.get('exploitation_confidence', self.config.zero_value)
        cluster_bonus = impair_bias.get(f'c{cluster_id}_specialization_bonus', self.config.zero_value) * spec_params.get('confidence_multiplier', self.config.confidence_multiplier_02)

        impair_bias['exploitation_confidence'] = min(self.config.one_value, base_confidence + cluster_bonus)
        impair_bias[f'c{cluster_id}_specialization_applied'] = True
        impair_bias[f'c{cluster_id}_total_bonus'] = cluster_bonus

        return impair_bias

