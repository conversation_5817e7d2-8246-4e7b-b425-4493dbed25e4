MÉTHODE : _generate_pb_sequence
LIGNE DÉBUT : 5000
SIGNATURE : def _generate_pb_sequence(self, target_pb: str, sequence_length: int, generation_space: Dict) -> List[str]:
================================================================================

    def _generate_pb_sequence(self, target_pb: str, sequence_length: int, generation_space: Dict) -> List[str]:
        """
        Génère une séquence basée sur prédiction P/B spécifique

        LONGUEUR FIXE : Toujours 4 P/B selon spécifications AZR

        Args:
            target_pb: 'P' pour Player, 'B' pour Banker
            sequence_length: Ignoré - longueur fixe à 4 P/B
            generation_space: Espace de génération avec contexte
        """
        sequence = []

        # Analyser les patterns récents pour déterminer la stratégie
        pbt_sequence = generation_space.get('indices_analysis', {}).get('pbt', {}).get('pbt_sequence', [])

        # Stratégie basée sur les patterns récents
        if len(pbt_sequence) >= 3:
            recent_pb = [r for r in pbt_sequence[-3:] if r in ['P', 'B']]

            if len(recent_pb) >= 2:
                # Si alternance récente, continuer l'alternance
                if recent_pb[-1] != recent_pb[-2]:
                    for i in range(self.config.rollout2_fixed_length):
                        if i == 0:
                            sequence.append(target_pb)
                        else:
                            # Alterner après le premier
                            last = sequence[-1]
                            sequence.append('B' if last == 'P' else 'P')
                else:
                    # Si série récente, forcer le target puis alterner
                    sequence.append(target_pb)
                    for i in range(1, self.config.rollout2_fixed_length):
                        last = sequence[-1]
                        sequence.append('B' if last == 'P' else 'P')
            else:
                # Pas assez de données, séquence simple
                for i in range(self.config.rollout2_fixed_length):
                    sequence.append(target_pb if i % 2 == 0 else ('B' if target_pb == 'P' else 'P'))
        else:
            # Séquence de base : commencer par target puis alterner
            for i in range(self.config.rollout2_fixed_length):
                sequence.append(target_pb if i % 2 == 0 else ('B' if target_pb == 'P' else 'P'))

        return sequence

