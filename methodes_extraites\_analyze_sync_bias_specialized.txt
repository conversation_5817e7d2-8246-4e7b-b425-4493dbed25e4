MÉTHODE : _analyze_sync_bias_specialized
LIGNE DÉBUT : 1700
SIGNATURE : def _analyze_sync_bias_specialized(self, hands_data: List, cluster_id: int, spec_params: Dict) -> Dict:
================================================================================

    def _analyze_sync_bias_specialized(self, hands_data: List, cluster_id: int, spec_params: Dict) -> Dict:
        """
        🎯 ANALYSE SYNC SPÉCIALISÉE GÉNÉRIQUE - Utilise paramètres centralisés

        Applique la spécialisation selon les paramètres centralisés dans AZRConfig.
        """
        # Utiliser la méthode de base
        sync_bias = self._analyze_sync_alternation_bias(hands_data)

        # Récupérer la fenêtre récente spécialisée du cluster
        cluster_recent_window = self.config.get_cluster_recent_window_size(cluster_id)

        # Appliquer la spécialisation selon les paramètres centralisés
        alternation_breaks = sync_bias.get('sync_alternation_breaks', [])
        if len(alternation_breaks) >= cluster_recent_window:
            recent_breaks = alternation_breaks[-cluster_recent_window:]

            # Bonus spécialisation selon paramètres centralisés
            specialization_bonus = self.config.zero_value
            for break_length in recent_breaks:

                # Appliquer les critères spécifiques au cluster
                if cluster_id in [2, 3, 4]:  # Clusters patterns
                    max_length = spec_params.get('max_length', 10)
                    if break_length <= max_length:
                        specialization_bonus += spec_params.get('bonus', self.config.zero_value)
                elif cluster_id == 6:  # Cluster SYNC/DESYNC
                    specialization_bonus += spec_params.get('rupture_bonus', spec_params.get('bonus', self.config.zero_value))
                else:  # Autres clusters
                    specialization_bonus += spec_params.get('bonus', self.config.zero_value)

            sync_bias[f'c{cluster_id}_specialization_bonus'] = min(self.config.one_value, specialization_bonus)
            sync_bias[f'c{cluster_id}_recent_breaks_count'] = len(recent_breaks)
            sync_bias[f'c{cluster_id}_recent_window_applied'] = cluster_recent_window

        # Confiance finale avec bonus spécialisation
        base_confidence = sync_bias.get('exploitation_confidence', self.config.zero_value)
        cluster_bonus = sync_bias.get(f'c{cluster_id}_specialization_bonus', self.config.zero_value) * spec_params.get('confidence_multiplier', self.config.confidence_multiplier_02)

        sync_bias['exploitation_confidence'] = min(self.config.one_value, base_confidence + cluster_bonus)
        sync_bias[f'c{cluster_id}_specialization_applied'] = True
        sync_bias[f'c{cluster_id}_total_bonus'] = cluster_bonus

        return sync_bias

