MÉTHODE : _calculate_confidence_level
LIGNE DÉBUT : 8111
SIGNATURE : def _calculate_confidence_level(self, global_strength: float, valid_strengths: int) -> str:
================================================================================

    def _calculate_confidence_level(self, global_strength: float, valid_strengths: int) -> str:
        """Calcule le niveau de confiance basé sur la force globale et le nombre de types valides"""

        if global_strength >= 0.8 and valid_strengths >= 4:
            return 'VERY_HIGH'
        elif global_strength >= 0.6 and valid_strengths >= 3:
            return 'HIGH'
        elif global_strength >= 0.4 and valid_strengths >= 2:
            return 'MEDIUM'
        elif global_strength >= 0.2:
            return 'LOW'
        else:
            return 'VERY_LOW'

